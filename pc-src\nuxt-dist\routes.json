[{"name": "coupon_center", "path": "/coupon_center", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\coupon_center.vue", "chunkName": "pages/coupon_center"}, {"name": "evaluation", "path": "/evaluation", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\evaluation.vue", "chunkName": "pages/evaluation"}, {"name": "goods_cate", "path": "/goods_cate", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\goods_cate.vue", "chunkName": "pages/goods_cate"}, {"name": "goods_coupon", "path": "/goods_coupon", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\goods_coupon.vue", "chunkName": "pages/goods_coupon"}, {"name": "goods_list", "path": "/goods_list", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\goods_list.vue", "chunkName": "pages/goods_list"}, {"name": "goods_presell", "path": "/goods_presell", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\goods_presell.vue", "chunkName": "pages/goods_presell"}, {"name": "goods_search", "path": "/goods_search", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\goods_search.vue", "chunkName": "pages/goods_search"}, {"name": "goods_seckill", "path": "/goods_seckill", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\goods_seckill.vue", "chunkName": "pages/goods_seckill"}, {"name": "login", "path": "/login", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\login.vue", "chunkName": "pages/login"}, {"name": "logistics", "path": "/logistics", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\logistics.vue", "chunkName": "pages/logistics"}, {"name": "logistics_delivery", "path": "/logistics_delivery", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\logistics_delivery.vue", "chunkName": "pages/logistics_delivery"}, {"name": "merchant_settled", "path": "/merchant_settled", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\merchant_settled.vue", "chunkName": "pages/merchant_settled"}, {"name": "news_list", "path": "/news_list", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\news_list.vue", "chunkName": "pages/news_list"}, {"name": "order_confirm", "path": "/order_confirm", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\order_confirm.vue", "chunkName": "pages/order_confirm"}, {"name": "order_detail", "path": "/order_detail", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\order_detail.vue", "chunkName": "pages/order_detail"}, {"name": "order_stay_detail", "path": "/order_stay_detail", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\order_stay_detail.vue", "chunkName": "pages/order_stay_detail"}, {"name": "payment", "path": "/payment", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\payment.vue", "chunkName": "pages/payment"}, {"name": "privacy_agreement", "path": "/privacy_agreement", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\privacy_agreement.vue", "chunkName": "pages/privacy_agreement"}, {"name": "qualifications", "path": "/qualifications", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\qualifications.vue", "chunkName": "pages/qualifications"}, {"name": "refund", "path": "/refund", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\refund.vue", "chunkName": "pages/refund"}, {"name": "refund_confirm", "path": "/refund_confirm", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\refund_confirm.vue", "chunkName": "pages/refund_confirm"}, {"name": "refund_detail", "path": "/refund_detail", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\refund_detail.vue", "chunkName": "pages/refund_detail"}, {"name": "refund_goods", "path": "/refund_goods", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\refund_goods.vue", "chunkName": "pages/refund_goods"}, {"name": "refund_logistics", "path": "/refund_logistics", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\refund_logistics.vue", "chunkName": "pages/refund_logistics"}, {"name": "reservation", "path": "/reservation", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\reservation.vue", "chunkName": "pages/reservation"}, {"name": "reservation_info", "path": "/reservation_info", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\reservation_info.vue", "chunkName": "pages/reservation_info"}, {"name": "shop_more", "path": "/shop_more", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\shop_more.vue", "chunkName": "pages/shop_more"}, {"name": "shop_street", "path": "/shop_street", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\shop_street.vue", "chunkName": "pages/shop_street"}, {"name": "shopping_cart", "path": "/shopping_cart", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\shopping_cart.vue", "chunkName": "pages/shopping_cart"}, {"path": "/store", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\store.vue", "chunkName": "pages/store", "children": [{"name": "store", "path": "", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\store\\\\index.vue", "chunkName": "pages/store/index"}, {"name": "store-category", "path": "category", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\store\\\\category.vue", "chunkName": "pages/store/category"}, {"name": "store-storeCoupon", "path": "storeCoupon", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\store\\\\storeCoupon.vue", "chunkName": "pages/store/storeCoupon"}]}, {"path": "/user", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user.vue", "chunkName": "pages/user", "children": [{"name": "user", "path": "", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\index.vue", "chunkName": "pages/user/index"}, {"name": "user-address_list", "path": "address_list", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\address_list.vue", "chunkName": "pages/user/address_list"}, {"name": "user-balance", "path": "balance", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\balance.vue", "chunkName": "pages/user/balance"}, {"name": "user-collect", "path": "collect", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\collect.vue", "chunkName": "pages/user/collect"}, {"name": "user-integral", "path": "integral", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\integral.vue", "chunkName": "pages/user/integral"}, {"name": "user-invoice_list", "path": "invoice_list", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\invoice_list.vue", "chunkName": "pages/user/invoice_list"}, {"name": "user-my_coupon", "path": "my_coupon", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\my_coupon.vue", "chunkName": "pages/user/my_coupon"}, {"name": "user-order_list", "path": "order_list", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\order_list.vue", "chunkName": "pages/user/order_list"}, {"name": "user-presell_order", "path": "presell_order", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\presell_order.vue", "chunkName": "pages/user/presell_order"}, {"name": "user-refund_list", "path": "refund_list", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\refund_list.vue", "chunkName": "pages/user/refund_list"}, {"name": "user-refund_select", "path": "refund_select", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\refund_select.vue", "chunkName": "pages/user/refund_select"}, {"name": "user-settle_record", "path": "settle_record", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\user\\\\settle_record.vue", "chunkName": "pages/user/settle_record"}]}, {"name": "goods_detail-id", "path": "/goods_detail/:id", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\goods_detail\\\\_id\\\\index.vue", "chunkName": "pages/goods_detail/_id/index"}, {"name": "goods_presell_detail-id", "path": "/goods_presell_detail/:id", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\goods_presell_detail\\\\_id\\\\index.vue", "chunkName": "pages/goods_presell_detail/_id/index"}, {"name": "goods_ranking-cate_id", "path": "/goods_ranking/:cate_id?", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\goods_ranking\\\\_cate_id.vue", "chunkName": "pages/goods_ranking/_cate_id"}, {"name": "goods_seckill_detail-id", "path": "/goods_seckill_detail/:id", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\goods_seckill_detail\\\\_id\\\\index.vue", "chunkName": "pages/goods_seckill_detail/_id/index"}, {"name": "news_detail-id", "path": "/news_detail/:id", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\news_detail\\\\_id\\\\index.vue", "chunkName": "pages/news_detail/_id/index"}, {"name": "index", "path": "/", "component": "C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\pc-src\\\\pages\\\\index.vue", "chunkName": "pages/index"}]