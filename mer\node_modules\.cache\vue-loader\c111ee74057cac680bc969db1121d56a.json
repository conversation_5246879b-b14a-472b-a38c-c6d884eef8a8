{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue?vue&type=style&index=0&id=f7b5f20a&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue", "mtime": 1750422426707}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\css-loader\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.drag {\r\n  cursor: move;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n.add-time {\r\n  color: var(--prev-color-primary);\r\n  cursor: pointer;\r\n}\r\n\r\n.reservation-times-box {\r\n  margin-top: 10px;\r\n  padding: 10px 20px;\r\n  width: 100%;\r\n  background-color: #fafafa;\r\n  border-radius: 10px;\r\n\r\n  /deep/ .el-checkbox__label {\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n.acea-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-top: 14px;\r\n}\r\n\r\n.flex-1 {\r\n  flex: 1;\r\n}\r\n\r\n.customize-time {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  .relative {\r\n    position: relative;\r\n    &:hover .el-icon-error {\r\n      visibility: visible;\r\n    }\r\n  }\r\n  .el-icon-error {\r\n    visibility: hidden;\r\n    cursor: pointer;\r\n    font-size: 15px;\r\n    color: #999999;\r\n    position: absolute;\r\n    top: -5px;\r\n    right: 6px;\r\n  }\r\n}\r\n\r\n// 多规格设置\r\n.specifications {\r\n  .specifications-item:hover {\r\n    background-color: var(--prev-color-primary-light-9);\r\n  }\r\n\r\n  .specifications-item:hover .del {\r\n    display: block;\r\n  }\r\n\r\n  .specifications-item:last-child {\r\n    margin-bottom: 14px;\r\n  }\r\n\r\n  .specifications-item {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20px 15px;\r\n    transition: all 0.1s;\r\n    background-color: #fafafa;\r\n    margin-bottom: 10px;\r\n    border-radius: 4px;\r\n\r\n    .del {\r\n      display: none;\r\n      position: absolute;\r\n      right: 15px;\r\n      top: 15px;\r\n      font-size: 22px;\r\n      color: var(--prev-color-primary);\r\n      cursor: pointer;\r\n      z-index: 9;\r\n    }\r\n\r\n    .specifications-item-box {\r\n      position: relative;\r\n\r\n      .lineBox {\r\n        position: absolute;\r\n        left: 13px;\r\n        top: 30px;\r\n        width: 30px;\r\n        height: 45px;\r\n        border-radius: 6px;\r\n        border-left: 1px solid #dcdfe6;\r\n        border-bottom: 1px solid #dcdfe6;\r\n      }\r\n\r\n      .specifications-item-name {\r\n        .el-icon-info {\r\n          color: var(--prev-color-primary);\r\n          font-size: 12px;\r\n          margin-left: 5px;\r\n        }\r\n      }\r\n\r\n      .specifications-item-name-input {\r\n        width: 200px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.spec {\r\n  display: block;\r\n  margin: 5px 0;\r\n  position: relative;\r\n\r\n  .img-popover {\r\n    cursor: pointer;\r\n    width: 76px;\r\n    height: 76px;\r\n    padding: 6px;\r\n    margin-top: 12px;\r\n    background-color: #fff;\r\n    position: relative;\r\n    border: 1px solid #dcdfe6;\r\n    border-radius: 4px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    &:hover .img-del {\r\n      display: block;\r\n    }\r\n\r\n    .img-del {\r\n      display: none;\r\n      position: absolute;\r\n      right: 3px;\r\n      top: 3px;\r\n      font-size: 16px;\r\n      color: var(--prev-color-primary);\r\n      cursor: pointer;\r\n      z-index: 9;\r\n    }\r\n\r\n    .popper {\r\n      width: 100%;\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n\r\n    .img {\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    .popper-arrow,\r\n    .popper-arrow:after {\r\n      position: absolute;\r\n      display: block;\r\n      width: 0;\r\n      height: 0;\r\n      border-color: transparent;\r\n      border-style: solid;\r\n    }\r\n\r\n    .popper-arrow {\r\n      top: -13px;\r\n      border-top-width: 0;\r\n      border-bottom-color: #dcdfe6;\r\n      border-width: 6px;\r\n      filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));\r\n\r\n      &::after {\r\n        top: -5px;\r\n        margin-left: -6px;\r\n        border-top-width: 0;\r\n        border-bottom-color: #fff;\r\n        content: \" \";\r\n        border-width: 6px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-icon-error {\r\n    position: absolute;\r\n    display: none;\r\n    right: -3px;\r\n    top: -3px;\r\n    z-index: 9;\r\n    color: var(--prev-color-primary);\r\n  }\r\n}\r\n\r\n.priceBox {\r\n  width: 100%;\r\n}\r\n\r\n.tabPic {\r\n  width: 40px !important;\r\n  height: 40px !important;\r\n\r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n.pictrue {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 1px dotted rgba(0, 0, 0, 0.1);\r\n  margin-right: 15px;\r\n  display: inline-block;\r\n  position: relative;\r\n  cursor: pointer;\r\n\r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .btndel {\r\n    position: absolute;\r\n    z-index: 1;\r\n    width: 20px !important;\r\n    height: 20px !important;\r\n    left: 46px;\r\n    top: -4px;\r\n  }\r\n}\r\n.spec:hover {\r\n  .el-icon-error {\r\n    display: block;\r\n    z-index: 999;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.move-icon {\r\n  width: 30px;\r\n  cursor: move;\r\n  margin-right: 10px;\r\n}\r\n.move-icon .icondrag2 {\r\n  font-size: 26px;\r\n  color: #bbb;\r\n}\r\n\r\n.btndel {\r\n  position: absolute;\r\n  z-index: 1;\r\n  width: 20px !important;\r\n  height: 20px !important;\r\n  left: 46px;\r\n  top: -4px;\r\n\r\n  &.btnclose {\r\n    left: auto;\r\n    right: 0;\r\n    top: 0;\r\n  }\r\n}\r\n\r\n.addfont {\r\n  display: inline-block;\r\n  font-size: 12px;\r\n  font-weight: 400;\r\n  color: var(--prev-color-primary);\r\n  margin-left: 14px;\r\n  cursor: pointer;\r\n}\r\n\r\n.upLoadPicBox {\r\n  position: relative;\r\n\r\n  &.specPictrue {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  .upLoad {\r\n    -webkit-box-orient: vertical;\r\n    -moz-box-orient: vertical;\r\n    -o-box-orient: vertical;\r\n    -webkit-flex-direction: column;\r\n    -ms-flex-direction: column;\r\n    flex-direction: column;\r\n    line-height: 20px;\r\n  }\r\n\r\n  span {\r\n    font-size: 10px;\r\n  }\r\n}\r\n\r\n.rulesBox {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n\r\n  .item {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .addfont {\r\n    margin-top: 5px;\r\n    margin-left: 0px;\r\n    width: 100px;\r\n  }\r\n\r\n  ::v-deep .el-popover {\r\n    border: none;\r\n    box-shadow: none;\r\n    padding: 0;\r\n    margin-top: 5px;\r\n    line-height: 1.5;\r\n  }\r\n}\r\n\r\n// 服务包配置样式\r\n.service-package-config {\r\n  .basic-config {\r\n    .basic-package-card {\r\n      .card-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .advanced-packages {\r\n    .packages-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 20px;\r\n\r\n      h3 {\r\n        margin: 0;\r\n        color: #333;\r\n      }\r\n    }\r\n\r\n    .packages-row {\r\n      .package-card {\r\n        height: 100%;\r\n\r\n        .package-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n\r\n          .package-title {\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n          }\r\n        }\r\n\r\n        &.basic-card .package-title {\r\n          color: #52c41a;\r\n        }\r\n\r\n        &.standard-card .package-title {\r\n          color: #1890ff;\r\n        }\r\n\r\n        &.premium-card .package-title {\r\n          color: #722ed1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .extra-services-config {\r\n    margin-top: 30px;\r\n\r\n    .extra-service-card {\r\n      height: 100%;\r\n\r\n      .card-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .generate-specs-btn {\r\n    padding: 20px;\r\n    background: #f5f5f5;\r\n    border-radius: 6px;\r\n\r\n    .el-button {\r\n      margin: 0 10px;\r\n    }\r\n  }\r\n}\r\n\r\n// 预览对话框样式\r\n::v-deep .preview-dialog {\r\n  .el-message-box__content {\r\n    text-align: left;\r\n\r\n    pre {\r\n      background: #f5f5f5;\r\n      padding: 15px;\r\n      border-radius: 4px;\r\n      font-size: 12px;\r\n      line-height: 1.5;\r\n      max-height: 400px;\r\n      overflow-y: auto;\r\n    }\r\n  }\r\n}\r\n", null]}