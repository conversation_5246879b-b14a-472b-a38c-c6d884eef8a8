{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\index.vue?vue&type=template&id=3968db4b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\index.vue", "mtime": 1750420600754}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div class=\"clearfix\">\n      <el-tabs v-if=\"headTab.length > 0\" v-model=\"currentTab\">\n        <el-tab-pane\n          v-for=\"(item, index) in headTab\"\n          :key=\"index\"\n          :name=\"item.name\"\n          :label=\"item.title\"\n        />\n      </el-tabs>\n    </div>\n\n    <el-form\n      ref=\"formValidate\"\n      :key=\"currentTab\"\n      v-loading=\"fullscreenLoading\"\n      class=\"formValidate mt20\"\n      :rules=\"ruleValidate\"\n      :model=\"formValidate\"\n      label-width=\"130px\"\n      @submit.native.prevent\n    >\n      <!-- 商品信息 -->\n      <productInfo\n        v-if=\"currentTab === '1'\"\n        :formValidate=\"formValidate\"\n        :OneattrValue=\"OneattrValue\"\n        :attrValue=\"attrValue\"\n        :is_timed=\"is_timed\"\n        :timeVal=\"timeVal\"\n        :videoLink=\"videoLink\"\n        :timeVal2=\"timeVal2\"\n        :props=\"props\"\n        @changeTimed=\"switchTimed\"\n        @productCon=\"productCon\"\n        @getSpecsLst=\"getSpecsLst\"\n        @getSpecsList=\"getSpecsList\"\n        @generateHeader=\"generateHeader\"\n      />\n      <!--  规格设置  -->\n      <productSpecs\n        v-if=\"currentTab === '2' && formValidate.type !== 4\"\n        :formValidate=\"formValidate\"\n        :attrs=\"attrs\"\n        :oneFormBatch=\"oneFormBatch\"\n        :OneattrValue=\"OneattrValue\"\n        :ManyAttrValue=\"ManyAttrValue\"\n        :changeAttrValue=\"changeAttrValue\"\n        :attrValue=\"attrValue\"\n        :formThead=\"formThead\"\n        :formDynamic=\"formDynamic\"\n        :product_id=\"product_id\"\n        :cdkeyLibraryList=\"cdkeyLibraryList\"\n        @handleBlur=\"handleBlur\"\n        @handleFocus=\"handleFocus\"\n        @generateAttr=\"generateAttr\"\n        @handleAddRole=\"handleAddRole\"\n        @handleRemoveRole=\"handleRemoveRole\"\n        @attrChangeValue=\"attrChangeValue\"\n        @batchDel=\"batchDel\"\n        @getSelectedLiarbry=\"getSelectedLiarbry\"\n        @delAttrTable=\"delAttrTable\"\n        @delManyImg=\"delManyImg\"\n        @attrDetailChangeValue=\"attrDetailChangeValue\"\n        @setAttrs=\"setAttrs\"\n        @addVirtual=\"addVirtual\"\n        @seeVirtual=\"seeVirtual\"\n      />\n\n      <!-- 预约商品的规格设置 -->\n      <reservationSpecs\n        v-if=\"currentTab === '2' && formValidate.type === 4\"\n        ref=\"reservationSpecs\"\n        :formValidate=\"formValidate\"\n        :attrs=\"attrs\"\n        :OneattrValue=\"OneattrValue\"\n        :ManyAttrValue=\"ManyAttrValue\"\n        :changeAttrValue=\"changeAttrValue\"\n        :attrValue=\"attrValue\"\n        :formThead=\"formThead\"\n        :oneFormBatch=\"oneFormBatch\"\n        :formDynamic=\"formDynamic\"\n        :product_id=\"product_id\"\n        :cdkeyLibraryList=\"cdkeyLibraryList\"\n        @generateAttr=\"generateAttr\"\n        @handleAddRole=\"handleAddRole\"\n        @handleRemoveRole=\"handleRemoveRole\"\n        @delAttrTable=\"delAttrTable\"\n        @delManyImg=\"delManyImg\"\n        @attrChangeValue=\"attrChangeValue\"\n        @batchDel=\"batchDel\"\n        @attrDetailChangeValue=\"attrDetailChangeValue\"\n        @getSelectedLiarbry=\"getSelectedLiarbry\"\n        @setAttrs=\"setAttrs\"\n        @handleBlur=\"handleBlur\"\n        @handleFocus=\"handleFocus\"\n      >\n      </reservationSpecs>\n\n      <!-- 预约设置 -->\n      <reservationSetting\n        v-if=\"currentTab === '7'\"\n        ref=\"reservationSetting\"\n        :roterPre=\"roterPre\"\n        :formValidate=\"formValidate\"\n        :formList=\"formList\"\n        :formUrl=\"formUrl\"\n        @getFormList=\"getFormList\"\n        @getFormInfo=\"getFormInfo\"\n      >\n      </reservationSetting>\n\n      <!-- 商品详情 -->\n      <productDetail\n        v-if=\"currentTab === '3'\"\n        :formValidate=\"formValidate\"\n        @getEditorContent=\"getEditorContent\"\n      />\n\n      <!-- 营销设置 -->\n      <productMarket\n        v-if=\"currentTab === '4'\"\n        :formValidate=\"formValidate\"\n        :good-list=\"goodList\"\n        :OneattrValue=\"OneattrValue\"\n        :open_svip=\"open_svip\"\n        :manyTabDate=\"manyTabDate\"\n        :deduction_set=\"deduction_set\"\n        :extensionStatus=\"extensionStatus\"\n        :deductionStatus=\"deductionStatus\"\n        :deduction_ratio_rate=\"deduction_ratio_rate\"\n        :extension_two_rate=\"extension_two_rate\"\n        :manyTabTit=\"manyTabTit\"\n        :ManyAttrValue=\"ManyAttrValue\"\n        :svip_rate=\"svip_rate\"\n        :base-url=\"baseURL\"\n        :specValue=\"specValue\"\n        :formThead=\"formThead\"\n        @openRecommend=\"openRecommend\"\n      />\n      <!-- 参数设置-->\n      <product-param\n        v-if=\"currentTab === '5'\"\n        :formValidate=\"formValidate\"\n        :customSpecs=\"customSpecs\"\n        :sysSpecsSelect=\"sysSpecsSelect\"\n        :merSpecsSelect=\"merSpecsSelect\"\n        @getSpecsList=\"getSpecsList\"\n      />\n\n      <!-- 其他设置 -->\n      <productOther\n        v-if=\"currentTab === '6' && formValidate.type !== 4\"\n        :formValidate=\"formValidate\"\n        :deliveryList=\"deliveryList\"\n        :shippingList=\"shippingList\"\n        :guaranteeList=\"guaranteeList\"\n        :formList=\"formList\"\n        :formUrl=\"formUrl\"\n        :roterPre=\"roterPre\"\n        @addTem=\"addTem\"\n        @getFormList=\"getFormList\"\n        @getFormInfo=\"getFormInfo\"\n        @addServiceTem=\"addServiceTem\"\n      />\n\n      <!-- 预约商品的其他设置 -->\n      <reservationOther\n        v-if=\"currentTab === '6' && formValidate.type === 4\"\n        ref=\"reservationOther\"\n        :formValidate=\"formValidate\"\n        :deliveryList=\"deliveryList\"\n        :shippingList=\"shippingList\"\n        :guaranteeList=\"guaranteeList\"\n        :formList=\"formList\"\n        :formUrl=\"formUrl\"\n        :roterPre=\"roterPre\"\n        @addTem=\"addTem\"\n        @getFormList=\"getFormList\"\n        @getFormInfo=\"getFormInfo\"\n        @addServiceTem=\"addServiceTem\"\n      ></reservationOther>\n    </el-form>\n  </el-card>\n\n  <div class=\"footer\">\n    <el-button\n      v-show=\"currentTab > 1\"\n      class=\"submission\"\n      size=\"small\"\n      @click=\"handleSubmitUp\"\n      >{{ $t('上一步') }}</el-button>\n    <el-button\n      v-show=\"currentTab != 6\"\n      class=\"submission\"\n      size=\"small\"\n      @click=\"handleSubmitNest('formValidate')\"\n      >{{ $t('下一步') }}</el-button>\n    <el-button\n      v-show=\"currentTab == '6' || $route.query.id\"\n      :loading=\"loading\"\n      type=\"primary\"\n      class=\"submission\"\n      size=\"small\"\n      @click=\"handleSubmit('formValidate')\"\n      >{{ $t('提交') }}</el-button>\n    <el-button\n      :loading=\"loading\"\n      class=\"submission\"\n      size=\"small\"\n      @click=\"handlePreview('formValidate')\"\n      >{{ $t('预览') }}</el-button>\n  </div>\n\n  <!--属性选择弹窗-->\n  <el-dialog\n    v-if=\"attrShow\"\n    :visible.sync=\"attrShow\"\n    :title=\"$t('请选择商品规格')\"\n    width=\"320px\"\n  >\n    <attr-list\n      :attrs=\"attrsList\"\n      @activeData=\"activeAttr\"\n      @close=\"labelAttr\"\n      @subAttrs=\"subAttrs\"\n      v-if=\"attrShow\"\n    ></attr-list>\n  </el-dialog>\n  <!--添加服务保障模板-->\n  <guarantee-service ref=\"serviceGuarantee\" @get-list=\"getGuaranteeList\" />\n  <!--预览商品-->\n  <div v-if=\"previewVisible\">\n    <div class=\"bg\" @click.stop=\"previewVisible = false\" />\n    <preview-box\n      v-if=\"previewVisible\"\n      ref=\"previewBox\"\n      :preview-key=\"previewKey\"\n    />\n  </div>\n  <!-- 生成淘宝京东表单-->\n  <tao-bao ref=\"taoBao\" @info-data=\"infoData($event, 'taobao')\" />\n  <!--添加链接-->\n  <add-carMy\n    ref=\"addCarMy\"\n    :virtualList=\"virtualList\"\n    @changeVirtual=\"changeVirtual\"\n    @fixdBtn=\"fixdBtn\"\n    @closeCarMy=\"closeCarMy\"\n  ></add-carMy>\n  <!--添加卡密-->\n  <cdkey-library\n    ref=\"cdkeyLibrary\"\n    :cdkeyLibraryInfo=\"cdkeyLibraryInfo\"\n    :selectedLibrary=\"selectedLibrary\"\n    @handlerSubSuccess=\"handlerChangeCdkeyIdSubSuccess\"\n  ></cdkey-library>\n  <!--选择店铺推荐商品-->\n  <el-dialog\n    :visible.sync=\"recommendVisible\"\n    :title=\"$t('推荐商品列表')\"\n    width=\"900px\"\n  >\n    <goods-list\n      ref=\"goodslist\"\n      v-if=\"recommendVisible\"\n      :ischeckbox=\"true\"\n      :isGood=\"true\"\n      :product_id=\"product_id\"\n      :selectedArr=\"goodList\"\n      @getProductId=\"getRecommend\"\n      @close=\"closeRecommend\"\n    ></goods-list>\n  </el-dialog>\n  <templatesFrom\n    ref=\"templateForm\"\n    @getList=\"getShippingList\"\n  ></templatesFrom>\n</div>\n", null]}