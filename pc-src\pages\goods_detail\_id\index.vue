<template>
  <div class="goods_count">
    <div class="store-banner" v-if="storeDetail.top_banner">
      <img :src="storeDetail.top_banner" alt="">
    </div>
    <div class="menu-count">
      <div class="user-menu user-wrapper acea-row row-middle">
        <div class="menu-main acea-row row-middle">
          <div @click="goPage(menu,index)" @mouseenter="showCategory(index)" class="menu-item" v-for="(menu,index) in userMenu" :key="index">{{menu.title}}</div>
        </div>
        <div class="menu-search acea-row row-middle">
          <div class="text"><input type="text" placeholder="店内商品搜索" v-model="search"></div>
          <div class="bnt" @click="submit"><span class="iconfont icon-xiazai5"></span></div>
        </div>
        <div v-if="seen" class="category acea-row row-middle" @mouseleave="leave()">
          <div class="sort">
            <div
              class="item acea-row row-between-wrapper"
              v-for="(item, index) in category"
              :key="index"
            >
              <div class="name line1">{{ item.cate_name }}<span class="iconfont icon-you"></span></div>
              <div class="sortCon">
                <div class="erSort acea-row">
                  <div
                    class="sub-item line1"
                    v-for="(itemn, indexn) in item.children"
                    :key="indexn"
                    @click="goCategoryGoods(itemn.store_category_id)"
                  >
                    {{ itemn.cate_name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <nuxt-link class="moreBtn" :to="{path:'/store/category',query:{id:storeInfo.mer_id}}">查看全部商品</nuxt-link>
        </div>
      </div>
    </div>
    <div class="goods-detail">
      <div class="wrapper_1200 acea-row">
        <div class="goods-main">
          <div class="acea-row row-top" style="position: relative;">
            <div class="carousel">
              <img :src="storeInfo.slider_image[slideIndex]" class="preview"/>
              <client-only>
                <div v-swiper:carousel="swiperOption">
                  <div class="swiper-wrapper">
                    <div
                      v-for="(item, index) in storeInfo.slider_image"
                      :key="index"
                      :class="{ on: slideIndex === index }"
                      class="swiper-slide"
                      @mouseover="swiperMouseover(index)"
                    >
                      <img :src="item"/>
                    </div>
                  </div>
                  <div class="swiper-button-prev swiper-button-white"></div>
                  <div class="swiper-button-next swiper-button-white"></div>
                </div>
              </client-only>
              <div class="acea-row row-middle">
                <div class="btn" style="width:60px" @click="collect">
                <span
                  :class="[
                    'iconfont',
                    storeInfo.isRelation ? 'icon-yishoucang' : 'icon-shoucang2'
                  ]"
                ></span
                >{{ storeInfo.isRelation ? "已收藏" : "收藏" }}
                </div>
                <div class="btn contactBtn" @click="chatShow" v-if="mer_service.services_type== 1">
                  <span class="iconfont icon-kefu2"></span>联系客服
                </div>
                <div class="btn contactBtn" v-else-if="mer_service.services_type == 2 && mer_service.service_phone">
                  <el-tooltip popper-class="tps" effect="dark" :content="'客服电话：'+mer_service.service_phone" placement="right">  
                    <div><span class="iconfont icon-kefu2"></span>联系客服</div>
                  </el-tooltip>
                </div>
                <a class="btn contactBtn" v-else-if="mer_service.services_type== 4" :href="mer_service.mer_customer_link" target="blank">
                  <span class="iconfont icon-kefu2"></span>联系客服
                </a>
              </div>
            </div>
            <div class="text-wrapper">
              <div class="title">{{ storeInfo.store_name }}</div>
              <div class='integral_count' v-if="storeInfo.max_integral > 0">
                <span class='integral'>积分最高可抵扣{{storeInfo.max_integral}}元</span>
              </div>
              <div :[atmosphere_pic]="{ backgroundImage: `url(${storeInfo.atmosphere_pic})` }" class="acea-row row-middle money-wrapper">
                <div class="price">
                    ￥<span>{{
                    attrValueSelected
                        ? attrValueSelected.price
                        : storeInfo.price
                    }}</span>
                </div>
                <del v-if="!svipData.show_svip_price && !svipData.show_svip">￥{{
                    attrValueSelected
                    ? attrValueSelected.ot_price
                    : storeInfo.ot_price
                }}
                </del>
                <div v-if="svipData.show_svip_price && svipData.show_svip" class="acea-row svip-acea">
                  <div>￥{{
                    attrValueSelected
                    ? attrValueSelected.svip_price
                    : storeInfo.svip_price
                }}</div>
                  <div class="svip-image">
                    <img src="@/assets/images/svip.png" />
                  </div>
                </div>
                <div class="sales acea-row row-column row-center-wrapper">
                  （销量 {{ storeInfo.sales }}{{ storeInfo.unit_name }}）
                </div>
              </div>
              <div class="coupon-wrapper" v-if="makeCouponList.length">
                <div class="acea-row coupon-bd">
                  <div class="label">优惠券</div>
                  <div class="list" :class="couponHide ? '' : 'on'">
                    <div
                      v-for="(item,index) in makeCouponList"
                      :key="item.id"
                      class="acea-row row-middle item"
                    >
                      <div class="acea-row cell">
                        <div class="acea-row row-center-wrapper cell-left">
                          {{ item.type }}
                        </div>
                        <div class="acea-row row-center-wrapper cell-right line1">
                          满{{ item.use_min_price }}减{{ item.coupon_price }}
                        </div>
                      </div>
                      <div v-if="item.coupon_type == 1" class="time">
                        {{ item.use_start_time |timeYMD }}-{{ item.use_end_time |timeYMD }}
                      </div>
                      <div v-if="item.coupon_type == 0" class="time">领取后{{ item.coupon_time }}天内可用</div>
                      <button :disabled="item.issue" @click="getCoupon(index, item)">
                        {{ item.issue ? "已领取" : "领取" }}
                      </button>
                    </div>
                  </div>
                </div>
                <div
                  v-if="makeCouponList.length > 3"
                  class="acea-row row-right row-middle coupon-ft"
                >
                  <div class="button" @click="getCouponList">
                    {{
                      couponHide ? "收起" : "更多优惠"
                    }}<span
                    class="iconfont"
                    :class="couponHide ? 'icon-xiangshang' : 'icon-xiangxia'"
                  ></span>
                  </div>
                </div>
              </div>
              <nuxt-link v-if="storeInfo.top_pid" class="ranking" :to="`/goods_ranking/${storeInfo.top_pid}`">
                <div>{{ storeInfo.top_name }} · 第{{ storeInfo.top_num }}名</div>
                <i class="iconfont icon-you"></i>
              </nuxt-link>
              <!-- 服务包选择界面 -->
              <div v-if="storeInfo.type != 4 && isServiceProduct" class="service-packages">
                <!-- 主要服务包选择 -->
                <div class="package-selector">
                  <div class="package-title">选择服务包</div>
                  <div class="package-options">
                    <div
                      v-for="(pkg, key) in servicePackages"
                      :key="key"
                      class="package-option"
                      :class="{ active: selectedPackage === key }"
                      @click="selectPackage(key)"
                    >
                      <div class="package-header">
                        <div class="package-name">{{ key }}</div>
                        <div class="package-price">￥{{ pkg.price }}</div>
                      </div>
                      <div class="package-details">
                        <div class="package-delivery">{{ pkg.delivery_time }}交付</div>
                        <div class="package-revisions">{{ pkg.revisions }}次修改</div>
                        <div class="package-description">{{ pkg.description }}</div>
                      </div>
                      <div v-if="pkg.features && pkg.features.length" class="package-features">
                        <div v-for="feature in pkg.features" :key="feature" class="feature-item">
                          <i class="iconfont icon-xuanzhong4"></i>
                          {{ feature }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 额外服务选择 -->
                <div class="extra-services" v-if="extraServices.length">
                  <div class="extra-title">额外服务（可选，最多选择2项）</div>
                  <div class="extra-options">
                    <label
                      v-for="(service, serviceIndex) in extraServices"
                      :key="serviceIndex"
                      class="extra-option"
                      :class="{ disabled: selectedExtras.length >= 2 && !selectedExtras.includes(serviceIndex) }"
                    >
                      <input
                        type="checkbox"
                        v-model="selectedExtras"
                        :value="serviceIndex"
                        :disabled="selectedExtras.length >= 2 && !selectedExtras.includes(serviceIndex)"
                      />
                      <div class="extra-content">
                        <span class="extra-name">{{ service.name }}</span>
                        <span class="extra-price">+￥{{ service.price }}</span>
                      </div>
                    </label>
                  </div>
                </div>

                <!-- 总价显示 -->
                <div class="total-price">
                  <div class="price-breakdown">
                    <div class="base-price">基础服务：￥{{ selectedPackagePrice }}</div>
                    <div v-if="extraServicesPrice > 0" class="extra-price">额外服务：￥{{ extraServicesPrice }}</div>
                  </div>
                  <div class="final-price">总计：￥{{ totalPrice }}</div>
                </div>
              </div>

              <!-- 传统规格选择界面 -->
              <div v-else-if="storeInfo.type != 4" class="attribute">
                <div
                  v-for="(item, index) in productAttr"
                  :key="index"
                  class="acea-row size-wrapper"
                >
                  <div class="label">{{ item.attr_name }}</div>
                  <div class="acea-row list">
                    <label
                      v-for="(itm, idx) in item.attr_values"
                      :key="idx"
                      class="item"
                    >
                      <input
                        v-model="attrSelected[index]"
                        type="radio"
                        :name="index"
                        :value="itm"
                        :checked="!idx"
                        hidden
                      />
                      <div class="acea-row cont">
                        <div class="acea-row row-middle name">{{ itm }}</div>
                        <div class="iconfont icon-xuanzhong4"></div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
              <div v-if="shippingValue" class="number-wrapper acea-row">
                <div class="label" @mouseover="tempTitle = true" @mouseleave="tempTitle = false">运费 <span class="iconfont icon-duoshanghupc-shuomingdanchuang" v-if="infoData.temp.info"></span></div>
                <div class="guaranee_tel" :style="{display:((tempTitle || tempInfo) && infoData.temp.info)? 'block':'none'}"  @mouseover="tempInfo = true" @mouseleave="tempInfo = false">
                  <div class="content" style="white-space: pre-line;">
                    {{infoData.temp.info}}
                  </div>
                </div>
                <div class="counter-wrap">
                  {{shippingValue}}
                </div>
              </div>
              <div v-if="guarantee && guarantee.length" class="number-wrapper acea-row">
                <div class="label" @mouseover="guaranteeTitle = true" @mouseleave="guaranteeTitle = false">保障 <span class="iconfont icon-duoshanghupc-shuomingdanchuang"></span></div>
                <div class="guaranee_tel" :style="{display:(guaranteeTitle || guaranteeInfo)? 'block':'none'}"  @mouseover="guaranteeInfo = true" @mouseleave="guaranteeInfo = false">
                  <div class="content">
                    <div v-for="(item,index) in guarantee" class="item" :key="index">
                      <div class="name">{{item.guarantee_name}}</div>
                      <div class="info" style="white-space: pre-line;">{{item.guarantee_info}}</div>
                    </div>
                  </div>
                </div>
                <div class="guaranteeAttr">
                  <div class="atterTxt1" v-for="(item,index) in guarantee" :key="index">
                    <span class="iconfont icon-duoshanghupc-baozhang"></span>{{item.guarantee_name ? item.guarantee_name : ''}}
                  </div>
                </div>
              </div>
              <div class="number-wrapper acea-row">
                <div class="label">数量</div>
                <div class="counter-wrap">
                  <div class="counter">
                    <button
                      class="iconfont icon-shangpinshuliang-jian"
                      :disabled="count === 1 || !stock || (count<=storeInfo.once_min_count&&storeInfo.once_min_count>0)"
                      @click="minus"
                    ></button>
                    <input v-model="count" @input="inputNum"/>
                    <button
                      class="iconfont icon-shangpinshuliang-jia"
                      :disabled="count === stock || !stock || (count>=storeInfo.once_max_count&&storeInfo.once_max_count>0)"
                      @click="plus"
                    ></button>
                  </div>
                  <span v-if="storeInfo.type != 4">（库存 {{ stock }}{{ storeInfo.unit_name }}）</span>
                </div>
              </div>
              <div v-if="storeInfo.once_min_count || storeInfo.once_max_count" class="min-max">
                (<template v-if="storeInfo.once_min_count">{{ storeInfo.once_min_count }}件起购</template>
                <template v-if="storeInfo.once_min_count && storeInfo.once_max_count">，</template>
                <template v-if="storeInfo.once_max_count">最多{{ storeInfo.once_max_count }}件</template>)
              </div>
              
              <div class="button-wrapper" v-if="stock || storeInfo.type == 4">
                <button
                  v-if="storeInfo.type != 1&&storeInfo.type != 2&&storeInfo.type != 3&&storeInfo.type != 4&&!storeInfo.mer_form_id"
                  class="btn cart"
                  :disabled="!stock && storeInfo.type != 4"
                  @click="buy(0, $event)"
                >
                  加入购物车
                </button>
                <button class="btn" :disabled="!stock && storeInfo.type != 4" @click="buy(1, $event)">
                  立即购买
                </button>
              </div>
              <div class="button-wrapper" v-else-if="!stock && storeInfo.type != 4">
                <button class="btn btn-out" disabled>已售罄</button>
                <button class="btn btn-notify" @click="arrivalNotice">
                  <span class="iconfont icon-duoshanghupc-daohuotongzhi"></span>
                  到货通知
                </button>
              </div>
            </div>
          </div>
          <div class="detail-wrapper">
            <div class="detail-hd acea-row">
              <div class="tab acea-row">
                <div
                  class="item acea-row row-center-wrapper"
                  :class="{ on: tabIndex === 0 }"
                  @click="tab(0)"
                >
                  产品详情
                </div>
                <div
                  v-if="infoData.params.length > 0"
                  class="item acea-row row-center-wrapper"
                  :class="{ on: tabIndex === 1 }"
                  @click="tab(1)"
                >
                  商品参数
                </div>
                <div
                  class="item acea-row row-center-wrapper"
                  :class="{ on: tabIndex === 3 }"
                  @click="tab(3)"
                >
                  价格说明
                </div>
                <div
                  v-if="replyInfo.count > 0"
                  class="item acea-row row-center-wrapper"
                  :class="{ on: tabIndex === 2 }"
                  @click="tab(2)"
                >
                  累计评论({{ replyInfo.count }})
                </div>
              </div>
              <div
                class="acea-row row-center-wrapper qrcode-button"
                @mouseover="qrcodeShow = true"
                @mouseout="qrcodeShow = false"
              >
                <span class="iconfont icon-saoma"></span>手机购买<span
                :class="[
                  'iconfont',
                  qrcodeShow ? 'icon-xiangshang1' : 'icon-xiangxia2'
                ]"
              ></span>
                <div class="qrcode">
                  <div class="phoneBuy">
                    <client-only>
                      <vue-qr :text="$store.state.domain + 'pages/goods_details/index?id=' + storeInfo.product_id" :size="200" :margin="6" style="display: block"></vue-qr>
                    </client-only>
                  </div>
                </div>
              </div>
            </div>
            <div class="detail-bd">
              <div v-show="tabIndex === 0">
                <div
                  v-if="infoData.content.content || infoData.content.content.image"
                  class="detail-html"
                >
                  <div v-if="infoData.content.type === 0" v-html="infoData.content.content"></div>
                  <div v-if="infoData.content.type === 1" class="product_content">
                    <div v-if="infoData.content && infoData.content.content.title" class="title">{{infoData.content.content.title}}</div>
                    <div v-if="infoData.content && infoData.content.content.image" class="pictures">
                      <img v-for="(item,index) in infoData.content.content.image" :key="index" :src="item"/>
                    </div>
                  </div>
                </div>
                <div v-else class="nothing">
                  <img src="@/assets/images/noDetail.png"/>
                  <div>暂无商品详情</div>
                </div>
              </div>
              <div v-show="tabIndex === 1">
                <div class="productSpecs">
                  <div class="item" v-for="(item,index) in infoData.params" :key="index">
                    <div class="name">{{item.label}}</div>
                    <div class="val">
                      <span v-for="(itm,idx) in item.value" :key="idx">
                        {{itm}}
                        <span v-if="idx < item.value.length-1" class="valCont">、</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-show="tabIndex === 3 && priceRule.content">
                <div style="padding: 20px;">
                  <div v-html="priceRule.content"></div>
                </div>
              </div>
              <div v-show="tabIndex === 2" class="comment">
                <div class="comment-hd">
                  <div class="acea-row row-between-wrapper">
                    <div class="rate">
                      <span>{{ replyInfo.rate }}</span><span>满意</span>
                    </div>
                    <div class="acea-row row-middle score">
                      评分
                      <div class="cont">
                      <span
                        v-for="(v, i) in 5"
                        :key="i"
                        :class="{ on: i + 1 <= storeInfo.rate }"
                        class="iconfont icon-pingjia"
                      ></span>
                      </div>
                    </div>
                  </div>
                  <div class="menu">
                    <div
                      class="item"
                      :class="{ on: reply.type === 'count' }"
                      @click="replyTypeChange('count', replyInfo.stat.count)"
                    >
                      全部({{ replyInfo.stat.count }})
                    </div>
                    <div
                      class="item"
                      :class="{ on: reply.type === 'best' }"
                      @click="replyTypeChange('best', replyInfo.stat.best)"
                    >
                      好评({{ replyInfo.stat.best }})
                    </div>
                    <div
                      class="item"
                      :class="{ on: reply.type === 'middle' }"
                      @click="replyTypeChange('middle', replyInfo.stat.middle)"
                    >
                      中评({{ replyInfo.stat.middle }})
                    </div>
                    <div
                      class="item"
                      :class="{ on: reply.type === 'negative' }"
                      @click="replyTypeChange('negative', replyInfo.stat.negative)"
                    >
                      差评({{ replyInfo.stat.negative }})
                    </div>
                  </div>
                </div>
                <div class="comment-bd">
                  <template v-if="replyList.length>0">
                    <div v-for="item in replyList" :key="item.reply_id" class="item">
                      <div class="acea-row row-middle item-hd">
                        <div class="image">
                          <img v-if="item.avatar" :src="item.avatar"/>
                          <img v-else src="~assets/images/f.png" alt="">
                        </div>
                        <div class="text">
                          <div class="acea-row row-middle name">
                            {{ item.nickname }}
                            <div class="star">
                            <span
                              v-for="(v, i) in 5"
                              :key="i"
                              class="iconfont icon-pingjia"
                              :class="{ on: i + 1 <= item.rate }"
                            ></span>
                            </div>
                          </div>
                          <div>{{ item.create_time }}</div>
                        </div>
                      </div>
                      <div class="item-bd">
                        <div>{{ item.comment }}</div>
                        <div class="image-wrapper">
                          <div
                            v-for="(itm, idx) in item.pics"
                            :key="idx"
                            class="image"
                            @click="isDialog = true"
                          >
                            <el-image
                              style="width: 54px; height: 54px"
                              :src="itm"
                              :preview-src-list="item.pics"
                            ></el-image>
                          </div>
                        </div>
                        <div v-if="item.merchant_reply_content" class="reply">
                          <div class="item">
                            <span>店小二：</span>{{ item.merchant_reply_content }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                  <img
                    v-else="replyList.length<0"
                    src="@/assets/images/noEvaluate.png"
                  />
                </div>
                <div v-if="replyList.length" class="acea-row row-right">
                  <el-pagination
                    layout="prev, pager, next"
                    prev-text="上一页"
                    next-text="下一页"
                    :page-size="reply.limit"
                    :total="replyCount"
                    @current-change="callPaginate"
                    @prev-click="callPaginate"
                    @next-click="callPaginate"
                  ></el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="recom-section">
          <div class="user-info">
            <div class="store-basis">
              <div class="store-logo">
                <img :src="storeDetail.mer_avatar" alt="">
              </div>
              <div class="line1">
                <span v-if="storeDetail.is_trader" class="trader">自营</span>
                <span class="store-name line1">{{storeDetail.mer_name}}</span>
              </div>
            </div>
            <div class="store-info">
              <div class="items acea-row row-middle">
                <span class="titles">店铺评分</span>
                <div class="cont">
                        <span
                          v-for="(v, i) in 5"
                          :key="i"
                          :class="{ on: i + 1 <= score.number.toFixed(1) }"
                          class="iconfont icon-pingjia star"
                        ></span>
                </div>
              </div>
              <div class="items acea-row row-middle">
                <span class="titles">关注人数</span>
                <span class="desc">{{storeDetail.care_count < 10000 ? storeDetail.care_count : (storeDetail.care_count/10000).toFixed(1)+'万'}}人</span>
              </div>
              <div v-if="mer_service.service_phone && mer_service.services_type == 2" class="items acea-row row-middle">
                <span class="titles">联系电话</span>
                <span class="desc">{{mer_service.service_phone}}</span>
              </div>
              <div v-if="storeDetail.isset_certificate" class="items acea-row row-middle">
                <span class="titles">店铺资质</span>
                <nuxt-link class="desc" :to="{path:'/qualifications',query:{id:storeDetail.mer_id,storeName:storeDetail.mer_name}}">
                  <img class="store_qualify" src="~/assets/images/store_qualify.png" alt="">
                  <span class="license">企业营业执照</span>
                </nuxt-link>
              </div>
            </div>
            <div class="store-favorites">
              <button class="collection" @click="goStore">进店逛逛</button>
              <button class="collection" :class="storeDetail.care ? 'care' : ''" @click="followToggle(mer_id)">{{storeDetail.care ? '已收藏' : '收藏店铺'}}</button>
            </div>
          </div>
          <div class="store-recommend" v-if="goodsList && goodsList.length">
            <div class="title"><span>店铺推荐</span></div>
            <div class="list">
              <nuxt-link
                v-for="(item, index) in goodsList"
                :key="index"
                :to="`/goods_detail/${item.product_id}`"
                class="item"
              >
                <div class="image">
                  <img :src="item.image" />
                </div>
                <div class="text">
                  <div class="name">{{ item.store_name }}</div>
                  <div class="acea-row row-between-wrapper">
                    <div class="money">
                      ￥<span>{{ item.price }}</span>
                    </div>
                    <div class="sales">销量 {{ item.sales }}</div>
                  </div>
                </div>
              </nuxt-link>
            </div>
          </div>
        </div>
      </div>
    </div>
    <chat-room
      v-if="chatPopShow"
      :chatId="storeInfo.mer_id"
      @close="chatPopShow = false"
    ></chat-room>
  </div>

</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import countDown from "@/components/countDown";
import ChatRoom from "@/components/ChatRoom";
export default {
  components: {countDown,ChatRoom},
  auth: false,
  data() {
    return {
      chatPopShow: false,
      moneyLevel: -1,
      showCode: false,
      serviceCode: false,
      search: '',
      show_guaranee: false,
      userMenu:[
        {
          key:0,
          link:'/store',
          title:'店铺首页',
        },
        {
          key:4,
          title:'全部分类',
          link:'/store/category'
        },
        {
          key:1,
          title:'领优惠券',
          link:'/store/storeCoupon'
        }
      ],
      categoryList: [],
      category: [], //店铺分类
      seen: false,
      menuCur: '',
      swiperOption: {
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev"
        },
        slidesPerView: "auto",
        observer: true,
        observeParents: true
      },
      slideIndex: 0,
      tabIndex: 0,
      reply: {
        type: 'count',
        page: 1,
        limit: 20,
        loading: false,
        finished: false
      },
      coupon: {
        page: 1,
        limit: 3,
        finished: false
      },
      attrSelected: [],
      attrValueSelected: null,
      count: 1,
      stock: 1,
      unique: "",
      couponHide: false,
      codeUrl: "",
      qrcodeShow: false,
      isDialog: false,
      // 服务包相关数据
      selectedPackage: 'Basic',
      selectedExtras: [],
      replyCount: 0,
      replyList: [],
      replyInfo: {
        stat: {}
      },
      couponList: [],
      QRcode: {},
      svipData: {},
      priceRule: {},
      guaranteeTitle: false,
      guaranteeInfo: false,
      tempTitle: false,
      tempInfo: false,
      slideNum: 0,
      storeDetail: {},
      mer_service: {},
      goodsList: [],
      guarantee: [],
      shipping: '',
      sku_id: ""
    };
  },
  filters: {
    timeYMD: function (value) {
      if (value) {
        var newDate = /\d{4}-\d{1,2}-\d{1,2}/g.exec(value)
        return newDate[0]
      }
    }
  },
  computed: {
    makeCouponList() {
      return this.couponList.map(value => {
        switch (value.type) {
          case 0:
            value.type = "店铺券";
            break;
          case 1:
            value.type = "商品券";
            break;
        }
        return value;
      });
    },
    score: function() {
      let store = this.storeDetail,
        score = {
          star: 0,
          number: 0
        };
      if ('postage_score' in store) {
        score.number = (parseFloat(store.postage_score) + parseFloat(store.product_score) + parseFloat(store.service_score)) / 3;
        score.star = score.number / 5 * 100;
      }
      return score;
    },
    atmosphere_pic() {
      return this.storeInfo.atmosphere_pic ? 'style' : null;
    },
    // 判断是否为服务包商品
    isServiceProduct() {
      return this.productAttr.some(attr => attr.attr_name === 'Packages');
    },
    // 解析服务包数据
    servicePackages() {
      const packagesAttr = this.productAttr.find(attr => attr.attr_name === 'Packages');
      if (!packagesAttr) return {};

      const packages = {};
      packagesAttr.attr_values.forEach(packageName => {
        // 从productValue中找到对应的包数据
        const packageKey = this.findPackageKey(packageName);
        if (packageKey && this.productValue[packageKey]) {
          const packageData = this.productValue[packageKey];
          packages[packageName] = {
            price: packageData.price || 0,
            delivery_time: this.extractDeliveryTime(packageData) || '3天',
            revisions: this.extractRevisions(packageData) || '2',
            description: this.extractDescription(packageData) || `${packageName}服务包`,
            features: this.extractFeatures(packageData) || []
          };
        }
      });
      return packages;
    },
    // 解析额外服务
    extraServices() {
      const services = [];
      this.productAttr.forEach((attr, index) => {
        if (attr.attr_name.startsWith('extra services')) {
          attr.attr_values.forEach(serviceName => {
            const serviceKey = this.findServiceKey(serviceName, index);
            if (serviceKey && this.productValue[serviceKey]) {
              services.push({
                name: serviceName,
                price: this.productValue[serviceKey].price || 0,
                category: this.extractServiceCategory(attr.attr_name)
              });
            }
          });
        }
      });
      return services;
    },
    // 选中的服务包价格
    selectedPackagePrice() {
      return this.servicePackages[this.selectedPackage]?.price || 0;
    },
    // 额外服务总价
    extraServicesPrice() {
      return this.selectedExtras.reduce((total, serviceIndex) => {
        return total + (this.extraServices[serviceIndex]?.price || 0);
      }, 0);
    },
    // 总价
    totalPrice() {
      return this.selectedPackagePrice + this.extraServicesPrice;
    }
  },
  watch: {
    productAttr: {
      immediate: true,
      handler(attr) {
        let value = [];
        if (attr.length) {
          for (var key in this.productValue) {
            if (this.productValue[key].is_default_select == 1) {
              value = this.productAttr.length ? key.split(",") : [];
              this.attrSelected[0] = value;
              break;
            }
				}
        attr.forEach((value, index) => {
          this.attrSelected[index] = value.attr_values[0];
        });

        // 如果是服务包商品，初始化服务包选择
        if (this.isServiceProduct) {
          const packagesAttr = attr.find(item => item.attr_name === 'Packages');
          if (packagesAttr && packagesAttr.attr_values.length > 0) {
            this.selectedPackage = packagesAttr.attr_values[0];
          }
        }

        console.log(this.attrSelected)
        } else {
          this.unique = this.productValue[""].unique
          this.sku_id = this.productValue[""].value_id
        }
      }
    },
    attrValueSelected(n){
      if(n.image) {
        this.slideNum++
        if(this.slideNum <= 1){
          this.storeInfo.slider_image.unshift(n.image)
        }else{
          this.storeInfo.slider_image[0] = n.image
        } 
        this.slideIndex = 0;
      }
    },
    attrSelected: {
      immediate: true,
      handler(attr) {
        if (attr.length) {
          let name = attr.join(),
            value = this.productValue[name];
          if (value && value.is_default_select) {
            this.attrValueSelected = value;
            this.stock = value.stock;
            this.unique = value.unique;
            this.sku_id = value.value_id;
          }
        }else {
          this.stock = this.storeInfo.stock;
          this.unique = this.productValue[""].unique;
          this.sku_id = this.productValue[""].value_id;
        }
      }
    }
  },
  async asyncData({error, app, params, query}) {
    try {
      let [goods,info] = await Promise.all([
        app.$axios.get(`/api/store/product/detail/${params.id}`),
        app.$axios.get(`/api/store/product/show/${params.id}`),
      ]);
      return {
        storeInfo: goods.data,
        productAttr: goods.data.attr,
        productValue: goods.data.sku,
        infoData: info.data,
        goodsList: info.data.merchant.recommend,
        storeDetail: info.data.merchant,
        mer_service: info.data.merchant.services_type,
        datatime: Number(query.time),
        count: goods.data.once_min_count || 1,
        id: params.id,
        mer_id: goods.data.mer_id,
        shippingValue: info.data.temp ? info.data.temp.name : '',
        guarantee: info.data.guaranteeTemplate ? info.data.guaranteeTemplate : [],
        shipping: info.data.temp ? info.data.temp.info : '',
        svipData: goods.data.show_svip_info
      };
    } catch (e) {
      error({statusCode: 500, msg: typeof e === 'string' ? e : '系统繁忙'});
    }
  },

  head() {
    return {
      title: this.storeInfo.store_name + '-' + this.$store.state.titleCon
    }
  },
  fetch({store}) {
    store.commit("isBanner", false);
    store.commit("isHeader", true);
    store.commit("isFooter", true);
  },
  beforeMount() {
    this.getCoupons();
    this.getCategory();
    this.getReply(1);
    this.getPriceRule();
    
  },
  mounted() {
    if (this.$auth.loggedIn) {
      this.moneyLevel = this.$auth.user.is_money_level
      this.$axios.get(`/api/pc/care`,{
          params:{
            type: 10,
            id: this.mer_id
          }
        }).then(res => {
          this.storeDetail.care = res.data.care
        });
    } else {
      this.moneyLevel = -1
    }
    this.$nextTick(() => {
      document.body.setAttribute("style", "background:#ffffff");
    });
  },
  beforeDestroy() {
    document.body.removeAttribute("style");
  },
  methods: {
    chatShow() {
      if(this.$auth.loggedIn){
        this.chatPopShow = true;
      }else{
        this.$store.commit("isLogin", true);
      }
    },
    // 服务包相关方法
    selectPackage(packageName) {
      this.selectedPackage = packageName;
      // 更新原有的attrSelected以保持兼容性
      const packagesAttrIndex = this.productAttr.findIndex(attr => attr.attr_name === 'Packages');
      if (packagesAttrIndex !== -1) {
        this.$set(this.attrSelected, packagesAttrIndex, packageName);
      }
    },
    // 辅助方法：从productValue中找到包对应的key
    findPackageKey(packageName) {
      for (const key in this.productValue) {
        if (key.includes(packageName)) {
          return key;
        }
      }
      return null;
    },
    // 辅助方法：从productValue中找到服务对应的key
    findServiceKey(serviceName, attrIndex) {
      for (const key in this.productValue) {
        if (key.includes(serviceName)) {
          return key;
        }
      }
      return null;
    },
    // 提取交付时间
    extractDeliveryTime(packageData) {
      // 从packageData中提取交付时间，可以根据实际数据结构调整
      return packageData.delivery_time || '3天';
    },
    // 提取修改次数
    extractRevisions(packageData) {
      return packageData.revisions || '2';
    },
    // 提取描述
    extractDescription(packageData) {
      return packageData.description || '';
    },
    // 提取功能特性
    extractFeatures(packageData) {
      return packageData.features || [];
    },
    // 提取服务类别
    extractServiceCategory(attrName) {
      const match = attrName.match(/\(([^)]+)\)/);
      return match ? match[1] : '';
    },
    //获取商品详情
    getGoodsDetails(){
      let that = this;
      that.$axios
        .get("/api/store/product/detail/" + that.id).then(res => {
          let goods = res.data
          that.storeInfo = res.data
          that.productAttr =  goods.data.attr
          that.productValue = goods.data.sku
          // that.goodsList = goods.data.merchant.recommend
          // that.storeDetail = goods.data.merchant
          that.mer_id = goods.data.mer_id
          // that.shippingValue = goods.data.temp ? goods.data.temp.name : ''
          that.guarantee = goods.data.guaranteeTemplate ? goods.data.guaranteeTemplate : []
          // that.shipping = goods.data.temp ? goods.data.temp.info : ''
          that.svipData = goods.data.show_svip_info
          that.getCategory(that.mer_id);
          that.$axios
          .get("/api/store/product/show/" + that.id).then(res => {
            let info = res.data
            that.$set(that.storeInfo, 'content', info.content);
						
						that.$set(that, 'guarantee', info.guaranteeTemplate ? info.guaranteeTemplate : []);
						that.$set(that, 'shippingValue', info.temp ? info.temp.name : '');
						that.$set(that.storeInfo, 'params', info.params);	
						that.$set(that, 'storeDetail', info.merchant);	
            that.$set(that, 'goodsList', info.merchant.recommend);	
						that.$set(that.storeInfo, 'spu_id', info.spu_id);	
						that.$set(that.storeInfo, 'community',info.community);	
						that.$set(that.storeInfo, 'top_name', info.top_name);	
						that.$set(that.storeInfo, 'atmosphere_pic', info.atmosphere_pic);	
            
        })
      })
      .catch(err => {
        this.$message.error(err);
      });
    },
    //获取店铺分类
    getCategory(){
      this.$axios
        .get(`/api/store/merchant/category/lst/${this.mer_id}`)
        .then(res => {
          this.category = res.data
        });
    },
    showCategory(index){
      if(index == 1){
        this.seen = true
      }else{
        return
      }
    },
    goPage(menu, index) {
      this.menuCur = menu.key
      this.$router.push({
        path: `${menu.link}`,
        query: {id: this.mer_id}
      });
    },
    // 价格说明
    getPriceRule() {
      this.$axios
        .get(`/api/store/product/price_rule/${this.storeInfo.cate_id}`)
        .then(res => {
          this.priceRule = res.data
        });
    },
    //进店逛逛
    goStore(){
      this.$router.push({ path: `/store?id=${this.mer_id}` });
    },
    followToggle(id){
      if (!this.$auth.loggedIn) {
        this.$store.commit("isLogin", true);
      }
      this.storeDetail.care ? this.unfollow(id) : this.follow(id);
    },
    unfollow(id){
      let that = this;
      that.$axios.post('/api/user/relation/delete',{
        type: 10,
        type_id: id
      }).then(res => {
        if (res.status === 200) {
          that.storeDetail.care = false;
          this.$message.success(res.message);
        }
      })
    },
    follow(id){
      let that = this;
      that.$axios.post('/api/user/relation/create',{
        type: 10,
        type_id: id
      }).then(res => {
        if (res.status === 200) {
          that.storeDetail.care = true;
          this.$message.success(res.message);
        }
      })
    },
    leave() {
      this.seen = false;
    },
    //获取优惠券
    getCoupons() {
      let that = this;
      let goodsArr = []
      let couponList = [];
      let activeList = []
      that.$axios.get(`/api/coupon/product`, {params: {ids: that.id}}).then(res => {
        goodsArr = res.data
        that.$axios.get(`/api/coupon/store/${that.storeInfo.mer_id}`).then(res => {
          couponList = goodsArr.concat(res.data)
          for (let i = 0; i < couponList.length; i++) {
            activeList.push(couponList[i]);
          }
          that.couponList = activeList
        })
          .catch(err => {
            that.$message.error(err);
          });
      })
        .catch(err => {
          that.$message.error(err);
        });
    },
    submit() {
      if (this.search.trim() !== '') {
        this.$router.push({path: '/store/category', query: {search: this.search.trim(), id: this.mer_id}});
        this.search = '';
      } else {
        this.$message.error("请输入要搜索的内容");
      }
    },
    goCategoryGoods(cateId, indexn) {
      this.$router.replace({path: '/store/category', query: {id: this.mer_id, cateId: cateId}});
    },
    changeVip(e) {
      this.showCode = e;
    },
    inputNum() {
      if(this.storeInfo.once_min_count>0 || this.storeInfo.once_max_count>0){
       if(this.storeInfo.once_min_count>0)this.count = parseInt(this.count) <= this.storeInfo.once_min_count>0 ? this.storeInfo.once_min_count : this.count;
       if(this.storeInfo.once_max_count>0)this.count = parseInt(this.count) >= this.storeInfo.once_max_count>0 ? this.storeInfo.once_max_count : this.count;
      }else{
        this.count = parseInt(this.count) >= this.stock ? this.stock : this.count;
      }
      this.count = parseInt(this.count) <= 1 ? 1 : this.count;
    },
    swiperMouseover(index) {
      this.slideIndex = index;
    },
    callPaginate(num) {
      this.reply.page = num;
      this.getReply(num);
    },
    tab(type) {
      this.tabIndex = type;
    },
    replyTypeChange(type, count) {
      this.replyCount = count;
      this.reply.type = type;
      this.reply.page = 1;
      this.replyList = [];
      this.$axios
        .get(`/api/store/product/reply/lst/${this.$route.params.id}`, {
          params: {
            page: this.reply.page,
            limit: this.reply.limit,
            type: type
          }
        })
        .then(res => {
          this.replyList = res.data.list;
          this.replyInfo = res.data;
        });
    },
    // 优惠券列表
    getCouponList() {
      this.couponHide = !this.couponHide;
    },
    // 加入购物车 | 立即购买
    buy(type, event) {
      if (typeof this.count === "string") {
        if (this.count.trim() === '') {
          return this.$message.error("请输入您要购买的数量");
        }
      }

      // 如果是服务包商品，确保选择了服务包
      if (this.isServiceProduct && !this.selectedPackage) {
        return this.$message.error("请选择服务包");
      }

      let btn = event.target;
      btn.disabled = true;
      if(this.storeInfo.type == 4){
        let sku_id = this.sku_id
        this.$router.push({
          path: `/reservation?id=${this.$route.params.id}&sku_id=${sku_id}&mer_id=${this.mer_id}&count=${this.count}`
        });
        return
      }

      // 构建购物车数据，包含服务包选择信息
      const cartData = {
        product_id: this.$route.params.id,
        cart_num: this.count,
        is_new: type,
        product_attr_unique: this.unique,
        product_type: 0
      };

      // 如果是服务包商品，添加额外信息
      if (this.isServiceProduct) {
        cartData.service_package = this.selectedPackage;
        cartData.extra_services = this.selectedExtras.map(index => this.extraServices[index]);
        cartData.total_price = this.totalPrice;
      }

      this.$axios
        .post("/api/user/cart/create", cartData)
        .then(res => {
          btn.disabled = false;
          this.$cookies.remove('cart_checked');
          if (type) {
            this.$router.push({
              path: `/order_confirm?new=1&cartId=${res.data.cart_id}`
            });
          } else {
            this.gainCount();
          }
        })
        .catch(err => {
          btn.disabled = false;
          this.$message.error(err);
        });
    },
    gainCount: function() {
      let that = this;
      that.$axios.get('/api/user/cart/count').then(res=>{
        that.$store.commit('cartNum', res.data[0].count || 0);
        this.$message.success("加入购物车成功")
      });
    },
    minus() {
      this.count--;
    },
    plus() {
      this.count++;
    },
    // 到货通知
    arrivalNotice(){
      let that = this;
      that.$axios.post('/api/store/product/increase_take',{
        unique: that.unique,type:1,product_id: that.$route.params.id
      }).then(res=>{
        this.$message.success(res.message)
      }).catch(err => {
        this.$message.error(err);
      })
    },
    // 领取优惠券
    getCoupon(index, item) {
      let that = this;
      if (!that.$auth.loggedIn) {
        that.$store.commit("isLogin", true);
      }
      that.$axios
        .post("/api/coupon/receive/" + item.coupon_id).then(res => {
        item.issue = true
        this.$message.success("领取优惠券成功");
      })
        .catch(err => {
          this.$message.error(err);
        });
    },
    // 获取评论
    getReply(num) {
      this.reply.loading = true;
      this.$axios
        .get(`/api/store/product/reply/lst/${this.$route.params.id}`, {
          params: {
            page: num,
            limit: this.reply.limit,
            type: this.reply.type
          }
        })
        .then(res => {
          this.replyList = res.data.list;
          this.replyCount = res.data.count
          this.replyInfo = res.data
        })
        .catch(err => {
          this.$message.error(err);
        });
    },
    // 收藏 | 取消收藏
    collect() {
      if (!this.$auth.loggedIn) {
       return this.$store.commit("isLogin", true);
      }
      if (this.storeInfo.isRelation) {
        this.$axios
          .post("/api/user/relation/delete", {
            type_id: this.$route.params.id,
            type: 0
          })
          .then(res => {
            this.$message.success("取消收藏成功");
          })
          .catch(err => {
            this.$message.error(err);
          });
      } else {
        this.$axios
          .post("/api/user/relation/create", {
            type_id: this.$route.params.id,
            type: 0
          })
          .then(res => {
            this.$message.success("收藏成功");
          })
          .catch(err => {
            this.$message.error(err);
          });
      }
      this.storeInfo.isRelation = !this.storeInfo.isRelation;
    }
  }
};
</script>

<style lang="scss" scoped>
.store-banner{
  width: 100%;
  height: 130px;
  img{
    object-fit: none;
    width: 100%;
    height: 100%;
  }
}

// 服务包样式
.service-packages {
  margin: 20px 0;

  .package-selector {
    margin-bottom: 20px;

    .package-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .package-options {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;

      .package-option {
        flex: 1;
        min-width: 200px;
        border: 2px solid #e8e8e8;
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #fff;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
        }

        &.active {
          border-color: #1890ff;
          background: #f6ffed;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .package-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          .package-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
          }

          .package-price {
            font-size: 18px;
            font-weight: bold;
            color: #ff4d4f;
          }
        }

        .package-details {
          margin-bottom: 10px;

          .package-delivery,
          .package-revisions {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
          }

          .package-description {
            font-size: 13px;
            color: #888;
            line-height: 1.4;
          }
        }

        .package-features {
          .feature-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #666;
            margin-bottom: 3px;

            .iconfont {
              color: #52c41a;
              margin-right: 5px;
              font-size: 10px;
            }
          }
        }
      }
    }
  }

  .extra-services {
    margin-bottom: 20px;

    .extra-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 10px;
      color: #333;
    }

    .extra-options {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .extra-option {
        display: flex;
        align-items: center;
        padding: 10px;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(.disabled) {
          border-color: #1890ff;
          background: #f6ffed;
        }

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        input[type="checkbox"] {
          margin-right: 10px;
        }

        .extra-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          .extra-name {
            font-size: 14px;
            color: #333;
          }

          .extra-price {
            font-size: 14px;
            font-weight: bold;
            color: #ff4d4f;
          }
        }
      }
    }
  }

  .total-price {
    padding: 15px;
    background: #f5f5f5;
    border-radius: 8px;

    .price-breakdown {
      margin-bottom: 10px;

      .base-price,
      .extra-price {
        font-size: 14px;
        color: #666;
        margin-bottom: 5px;
      }
    }

    .final-price {
      font-size: 18px;
      font-weight: bold;
      color: #ff4d4f;
      text-align: right;
    }
  }
}

.menu-count{
  width: 100%;
  height: 40px;
  background: #DFDFDF;
}
.store-name{
  display: inline-block;
  width: 120px;
  position: relative;
  top: 4px;
}
.user-menu{
  position: relative;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 1200px;
  margin: 0 auto;
  .category{
    position: absolute;
    top: 40px;
    left: 0;
    background-color: rgba(254,248,248,.96);
    width: 100%;
    padding: 40px 20px 20px;
    z-index: 10;
    .name{
      width: 130px;
      position: relative;
      padding-right: 20px;
      margin-right: 30px;
      cursor: pointer;
      .iconfont{
        font-size: 10px;
        position: absolute;
        right: 0;
        top: 3px;
        color: #282828;
      }
    }
    .sortCon{
      width: 1000px;
      .sub-item{
        margin: 0 15px 15px;
        color: #666666;
        cursor: pointer;
      }
    }
    .erSort{
      align-items: center;
    }
    .item{
      margin-bottom: 20px;
      align-items: baseline;
    }
    .moreBtn{
      color: #282828;
      font-size: 12px;
      width: 100px;
      height: 26px;
      line-height: 26px;
      text-align: center;
      border-radius: 13px;
      border: 1px solid #666666;
    }

  }
  .menu-main{
    width: 300px;
    height: 40px;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    .menu-item{
      display: inline-block;
      height: 26px;
      line-height: 26px;
      color: #282828;
      padding: 0 10px;
      cursor: pointer;
      &.active{
        color: #fff;
        background: #282828;
        color: #fff;
        border-radius: 15px;
      }
    }
  }
  .menu-search{
    width: 220px;
    height: 24px;
    background-color: #fff;
    border-radius: 17px;
    .text{
      width: 175px;
    }
    input{
      border: none;
      height: 24px;
      line-height: 24px;
      color: #999999;
      padding: 0 15px;
      border-radius: 17px;
      &:focus{
        border: none;
        outline: none;;
      }
    }
    .bnt{
      width: 44px;
      background-color: #282828;
      color: #fff;
      border-radius: 0 17px 17px 0;
      line-height: 24px;
      text-align: center;
      cursor: pointer;
    }
  }
}
.product_content .title{
  text-align: center;
  font-size: 18px;
  margin: 5px 0;
}
.productSpecs{
  overflow: hidden;
  padding: 30px;
  .item{
    float: left;
    display: flex;
    width: 50%;
    margin-bottom: 20px;
    .name{
      color: #999999;
      min-width: 90px;
      max-width: 120px;
      text-align: right;
      margin-right: 30px;
    }
    .val{
      color: #282828;
    }
  }
}
.goods-detail {
  padding-top: 40px;
  border-top: 1px solid #efefef;
  .goods-main {
    flex: 1;
    min-width: 0;
  }
  .carousel {
    width: 380px;
    .preview {
      display: block;
      width: 380px;
      height: 380px;
    }
    .swiper-container {
      padding-right: 25px;
      padding-left: 25px;
      margin-top: 10px;
      margin-bottom: 20px;
      .swiper-button-prev,
      .swiper-button-next {
        top: 0;
        width: 25px;
        height: 100%;
        margin-top: 0;
        background-color: rgba(0, 0, 0, 0.3);
        background-size: 12px 22px;
      }
      .swiper-button-prev {
        left: 0;
      }
      .swiper-button-next {
        right: 0;
      }
      .swiper-slide {
        width: 70px;
        height: 70px;
        border: 2px solid transparent;
        box-sizing: border-box;
        overflow: hidden;
        cursor: pointer;
        &.on {
          border-color: #e93323;
        }
        ~ .swiper-slide {
          margin-left: 10px;
        }
        img {
          display: block;
          width: 70px;
          height: 70px;
        }
      }
    }
    .btn {
      margin-right: 30px;
      font-size: 12px;
      color: #4b4b4b;
      cursor: pointer;
      position: relative;
      .qrcode1 {
        display: none;
        box-shadow: 0px 3px 16px rgba(0, 0, 0, 0.08);
        background: #fff;
        padding: 6px;
        position: relative;
        width: 100px;
        img{
          width: 100%;
        }
        &.contactService {
          position: absolute;
          left: 50%;
          top: 25px;
          z-index: 10;
          width: 100px;
          height: 100px;
          margin-left: -50px;
        }
      }
    }
    .contactBtn:hover {
      .qrcode1 {
        display: inline;
      }
    }
    .iconfont {
      margin-right: 6px;
      font-size: 14px;
      color: #e93323;
    }
  }
  .text-wrapper {
    flex: 1;
    min-width: 0;
    margin-left: 40px;
    .svip-acea{
      margin: 5px 0 0 10px;
      align-items: center;
    }
    .svip-image{
      width: 35px;
      height: 15px;
      margin: 3px 0 0 5px;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .title {
      font-size: 20px;
      line-height: 26px;
      color: #333333;
    }
    .integral_count{
      display: inline-block;
      margin-top: 18px;
      color: #FF6200;
      line-height: 27px;
      background: #FFF4E6;
      padding: 0 15px;
      border-radius: 2px;
    }
    .money-wrapper {
      height: 70px;
      padding-left: 32px;
      margin-top: 18px;
      background: url("~assets/images/money-back.png") center/cover no-repeat;
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
      color: #ffffff;
      del {
        margin: 0 0 5px 10px;
        font-size: 14px;
        line-height: 19px;
      }
      .price {
        font-size: 22px;
        span {
          font-weight: bold;
          font-size: 30px;
        }
      }
      .vip {
        width: 100px;
        height: 25px;
        border-radius: 2px;
        margin-left: 14px;
        background: linear-gradient(205deg, #fdcaa4 0%, #fce3c3 100%);
        overflow: hidden;
        font-size: 12px;
        color: #0f0f0f;
        .iconfont {
          width: 32px;
          background: url("~assets/images/svip.png") center/cover no-repeat;
          height: 25px;
        }
        .iconfontVip {
          width: 32px;
          background: url("~assets/images/vip.png") center/cover no-repeat;
          height: 25px;
        }
        .money {
          flex: 1;
          min-width: 0;
          span {
            font-size: 14px;
          }
        }
      }
      .sales {
        margin: 5px 0 0 5px;
        font-size: 12px;
        line-height: 19px;
        .num {
          margin-bottom: 3px;
          font-weight: bold;
          font-size: 18px;
        }
      }
    }
    .coupon-wrapper {
      background-color: #f7f7f7;
      .coupon-bd {
        padding-top: 18px;
        padding-bottom: 18px;
        font-size: 12px;
        color: #5a5a5a;
        .label {
          width: 80px;
          padding-left: 20px;
        }
        .list {
          flex: 1;
          min-width: 0;
          padding-right: 24px;
          max-height: 240px;
          overflow-y: auto;
          overflow-x: hidden;
          &.on {
            max-height: 100px;
            overflow: hidden;
          }
        }
        .item {
          margin-top: 11px;
          &:first-child {
            margin-top: 0;
          }
        }
        .cell-left {
          width: 65px;
        }
        .cell-right {
          flex: 1;
          min-width: 0;
          color: #e93323;
          max-width: 94px;
          padding: 0 3px;
        }
        .cell {
          width: 165px;
          height: 24px;
          background: url("~assets/images/coupon-back.png") left top/100% 100% no-repeat;
          font-size: 13px;
          color: #ffffff;
          &.svip {
            background-image: url("~assets/images/vipCou.png");
            color: #FDD7B4;
            .cell-right {
              color: #333333;
            }
          }
        }
        .time {
          flex: 1;
          min-width: 0;
          padding-right: 8px;
          padding-left: 8px;
          font-size: 12px;
          color: #727272;
        }
        button {
          border: none;
          border-bottom: 1px solid #e93323;
          background: none;
          font-size: 12px;
          color: #e93323;
          &:disabled {
            border-color: #c0c4cc;
            color: #c0c4cc;
            cursor: not-allowed;
          }
        }
      }
      .coupon-ft {
        height: 36px;
        padding-right: 24px;
        background-color: #f2f2f2;
        .button {
          font-size: 12px;
          color: #666666;
          cursor: pointer;
          .iconfont {
            margin-left: 7px;
            font-size: 10px;
            color: #666666;
          }
        }
      }
    }
    .ranking {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 32px;
      padding: 0 10px 0 76px;
      margin-top: 16px;
      background: url("~assets/images/top-ranking.png") 6px center/60px 20px no-repeat;
      background-color: #F1F1F1;
      font-size: 14px;
      color: #282828;
      .iconfont {
        font-size: 10px;
        color: #666666;
      }
    }
    .attribute {
      margin-top: 30px;
      .size-wrapper {
        .label {
          width: 78px;
          padding-left: 20px;
          font-size: 12px;
          color: #5a5a5a;
          margin-right: 2px;
          word-break: break-all;
          padding-top: 2px;
        }
        .list {
          flex: 1;
          min-width: 0;
        }
        .item {
          margin-right: 12px;
          margin-bottom: 16px;
          box-sizing: border-box;
          cursor: pointer;
          .cont {
            position: relative;
            height: 32px;
            border: 1px solid #d3d3d3;
          }
          &:hover {
            .cont {
              border-color: #e93323;
              color: #e93323;
            }
          }
          input:checked {
            + .cont {
              border-color: #e93323;
              color: #e93323;
              .iconfont {
                display: block;
              }
            }
          }
        }
        .image {
          width: 34px;
          height: 34px;
        }
        img {
          display: block;
          width: 100%;
          height: 100%;
        }
        .name {
          padding-right: 20px;
          padding-left: 20px;
          font-size: 12px;
        }
        .iconfont {
          position: absolute;
          right: -2px;
          bottom: -3px;
          display: none;
          font-size: 22px;
        }
      }
    }
    .number-wrapper {
      margin-top: 18px;
      position: relative;
      .guaranee_tel{
        position: absolute;
        top: 20px;
        left: 0;
        background: #ffffff;
        z-index: 10;
        padding: 0 24px 24px;
        display: none;
        box-shadow: 0px 3px 16px rgba(0, 0, 0, 0.08);
        .item{
          margin-top: 24px;
          .name{
            font-size: 16px;
            color: #000000;
          }
          .info{
            font-size: 12px;
            color: #969696;
            margin-top: 6px;
          }
        }
      }
      .icon-duoshanghupc-shuomingdanchuang{
        color: #E93323;
        font-size: 12px;
        position: relative;
      }
      .label{
        cursor: pointer;
      }
      .guaranteeAttr{
        display: inline-block;
        width: 445px;
      }
      .atterTxt1{
        display: inline-block;
        margin: 0 20px 11px 0;
        .icon-duoshanghupc-baozhang{
          display: inline-block;
          font-size: 14px;
          color: #E93323;
          margin-right: 2px;
        }
      }
      .label {
        width: 78px;
        padding-left: 20px;
        font-size: 12px;
        color: #5a5a5a;
        margin-right: 2px;
        word-break: break-all;
        padding-top: 2px;
      }
      .counter-wrap {
        flex: 1;
        min-width: 0;
        span {
          vertical-align: bottom;
          font-size: 14px;
          color: #5a5a5a;
        }
      }
      .counter {
        display: inline-block;
        border: 1px solid #d3d3d3;
        font-size: 0;
        button {
          width: 44px;
          height: 36px;
          border: none;
          background: none;
          outline: none;
          font-weight: inherit;
          font-size: 12px;
          font-family: inherit;
          color: #707070;
          vertical-align: middle;
          &:disabled {
            color: #d0d0d0;
            cursor: not-allowed;
          }
        }
        input {
          width: 64px;
          height: 36px;
          border: none;
          border-right: 1px solid #d3d3d3;
          border-left: 1px solid #d3d3d3;
          outline: none;
          font-weight: inherit;
          font-size: 18px;
          font-family: inherit;
          text-align: center;
          color: #5a5a5a;
          vertical-align: middle;
        }
      }
    }
    .min-max {
      padding: 15px 0 0 78px;
      font-size: 14px;
      line-height: 19px;
      color: #E93323;
    }
    .button-wrapper {
      margin-top: 46px;
      font-size: 0;
      .btn {
        width: 158px;
        height: 50px;
        border: 1px solid #e93323;
        border-radius: 4px;
        font-size: 16px;
        color: #e93323;
        &.btn-out{
          width: 120px;
          color: #ffffff;
          background: #D0D0D0;
        }
        &.btn-notify{
          width: 120px;
          border-color: #E93323;
          color: #E93323;
        }
        ~ .btn {
          margin-left: 18px;
        }
      }
      button {
        background: none;
        outline: none;
        vertical-align: middle;
        &:disabled {
          border-color: #ebeef5;
          color: #c0c4cc;
          cursor: not-allowed;
        }
        &.cart {
          background-color: #e93323;
          color: #ffffff;
          &:disabled {
            border-color: #fab6b6;
            background-color: #fab6b6;
          }
        }
        ~ button {
          margin-left: 18px;
        }
      }
      a {
        display: inline-block;
        background-color: #e93323;
        vertical-align: middle;
        line-height: 50px;
        text-align: center;
        &.btn {
          color: #ffffff;
        }
      }
    }
  }
  .detail-wrapper {
    margin-top: 70px;
    .detail-hd {
      background-color: #f7f7f7;
      .tab {
        flex: 1;
        min-width: 0;
      }
      .item {
        position: relative;
        height: 56px;
        padding-right: 30px;
        padding-left: 30px;
        font-size: 14px;
        color: #333333;
        cursor: pointer;
        &.on {
          background: url("~assets/images/checked.png") center top/100% 7px no-repeat;
          color: #e93323;
        }
        &::before {
          content: "";
          position: absolute;
          top: 18px;
          bottom: 18px;
          left: 0;
          width: 1px;
          border-left: 1px solid #d9d9d9;
        }
        &:first-child::before {
          display: none;
        }
        &:hover {
          color: #e93323;
        }
      }
      .qrcode-button {
        position: relative;
        width: 160px;
        height: 56px;
        background-color: #ededed;
        font-size: 14px;
        color: #333333;
        cursor: pointer;
        &:hover {
          .qrcode {
            display: block;
          }
        }
        .icon-saoma {
          margin-right: 6px;
          font-size: 13px;
          line-height: 1;
          color: #000000;
        }
        .icon-xiangxia2,
        .icon-xiangshang1 {
          margin-left: 10px;
          font-size: 10px;
          line-height: 12px;
          color: #d0d0d0;
        }
        .qrcode {
          position: absolute;
          z-index: 99;
          display: none;
          padding: 6px;
          background-color: #ffffff;
          border: 1px solid #ededed;
          margin-top: 6px;
          width: 160px;
          height: 160px;
          top: 50px;
          left: 0px;
          box-sizing: border-box;
        }
      }
    }  
    .comment {
      .comment-hd {
        padding-top: 30px;
        padding-bottom: 30px;
        .rate {
          font-size: 0;
          span {
            font-size: 14px;
            color: #e93323;
            ~ span {
              margin-left: 5px;
            }
          }
        }
        .score {
          font-size: 14px;
          color: #7e7e7e;
          .cont {
            margin-left: 8px;
          }
          .iconfont {
            font-size: 12px;
            color: #e6e6e6;
            ~ .iconfont {
              margin-left: 5px;
            }
            &.on {
              color: #e93323;
            }
          }
        }
        .menu {
          margin-top: 20px;
          font-size: 0;
          .item {
            display: inline-block;
            width: 86px;
            height: 34px;
            border-radius: 2px;
            background-color: #f7f7f7;
            font-size: 14px;
            line-height: 34px;
            text-align: center;
            color: #282828;
            cursor: pointer;
            &:hover {
              color: #e93323;
            }
            &.on {
              background-color: #e93323;
              color: #ffffff;
            }
            ~ .item {
              margin-left: 14px;
            }
          }
        }
      }
      .comment-bd {
        > img {
          width: 200px;
          margin: 50px auto;
        }
        .item {
          padding-top: 20px;
          padding-bottom: 20px;
          .item-hd {
            .image {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              overflow: hidden;
            }
            img {
              display: block;
              width: 100%;
              height: 100%;
            }
            .text {
              flex: 1;
              margin-left: 12px;
              font-size: 14px;
              color: #868686;
            }
            .name {
              margin-bottom: 4px;
              font-size: 16px;
              color: #282828;
            }
            .star {
              margin-left: 12px;
              font-size: 0;
            }
            .iconfont {
              font-size: 12px;
              color: #e6e6e6;

              &.on {
                color: #e93323;
              }
              ~ .iconfont {
                margin-left: 5px;
              }
            }
          }
          .item-bd {
            padding-bottom: 20px;
            border-bottom: 1px solid #e3e3e3;
            margin-left: 52px;
            font-size: 14px;
            color: #282828;
            > div {
              margin-top: 15px;
            }
            .image-wrapper {
              font-size: 0;
            }
            .image {
              display: inline-block;
              width: 54px;
              height: 54px;
              margin-right: 8px;
              margin-bottom: 10px;
              img {
                display: block;
                width: 100%;
                height: 100%;
              }
            }
            .reply {
              background-color: #f7f7f7;
              .item {
                padding: 7px 12px;
                font-size: 14px;
                color: #282828;
                span {
                  color: #e93323;
                }
              }
            }
          }
        }
      }
    }
  }

  .user-info{
    width: 210px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 350px;
    background: #fff;
    color: #282828;
    font-size: 14px;
    border: 1px solid #efefef;
    border-radius: 4px;
    padding: 0 20px;
    .store-basis{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-bottom: 1px dashed #ECECEC;
      height: 130px;
      .trader{
        display: inline-block;
        width: 32px;
        height: 18px;
        line-height: 18px;
        text-align: center;
        color: #E93323;
        color: #fff;
        background: #E93323;
        border-radius: 2px;
        margin-right: 3px;
        font-size: 12px;
        font-family: 'PingFang SC';
      }
    }
    .store-logo{
      width: 61px;
      height: 61px;
      margin-bottom: 15px;
      img{
        width: 61px;
        height: 61px;
        border-radius: 50%;
      }
    }
    .name{
      margin-top: 10px;
      padding: 0 15px;
    }
  }
  .store-info{
    padding: 15px 0 0;
    position: relative;
    border-bottom: 1px dashed #ECECEC;
    .service{
      right:210px;
      position:absolute;
      top:0;
      .ewm{
        width:140px;
        border:1px solid #eeeeee;
        background-color:#fff;
        padding: 10px 15px;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        align-items: center;
        color: #282828;
        // font-family: 'Microsoft YaHei';
        .tip{
          font-size: 14px;
          color: #666666;
          margin-top: 10px;
        }

      }
    }
    .items {
      font-size: 12px;
      color: #7e7e7e;
      margin-bottom: 15px;
      .iconfont{
        cursor: pointer;
      }
      .cont {
        margin-left: 8px;
      }
      .star {
        font-size: 12px;
        color: #e6e6e6;
        ~ .star {
          margin-left: 5px;
        }
        &.on {
          color: #e93323;
        }
      }
      .titles{
        color: #999999;
        font-size: 12px;
        margin-right: 15px;
      }
      .desc{
        color:#333333;
        position: relative;
        .store_qualify{
          width: 16px;
          height: 16px;
          &:hover + .license{
            display: inline-block;
          }
        }
      }
      .license{
        width: 90px;
        line-height: 26px;
        color: #fff;
        text-align: center;
        background: #282828;
        border-radius: 5px;
        position: absolute;
        top: 26px;
        left: -10px;
        display: none;
        &:before{
          content: '';
          display: inline-block;
          border: 3px solid transparent;
          border-bottom-color: #282828;
          position: absolute;
          top: -6px;
          left: 15px;
        }
      }
    }
  }
  .recom-section {
    align-self: flex-start;
    min-width: 210px;
    margin-left: 40px;
    .store-recommend{
      margin-top: 10px;
      padding: 0 20px;
      border: 1px solid #efefef;
      border-radius: 4px;
    }
    .title {
      height: 60px;
      font-size: 16px;
      line-height: 60px;
      text-align: center;
      color: #5a5a5a;
      span {
        position: relative;
        &::before {
          content: "";
          position: absolute;
          top: 50%;
          right: 100%;
          margin-right: 18px;
          width: 35px;
          height: 1px;
          border-top: 1px solid #efefef;
          transform: translateY(-50%);
        }
        &::after {
          content: "";
          position: absolute;
          top: 50%;
          left: 100%;
          margin-left: 18px;
          width: 35px;
          height: 1px;
          border-top: 1px solid #efefef;
          transform: translateY(-50%);
        }
      }
    }
    .item {
      display: block;
      width: 170px;
      margin-bottom: 20px;
      .image {
        width: 170px;
        height: 170px;
        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
      .text {
        .name {
          margin-top: 10px;
          margin-bottom: 10px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 13px;
          color: #666666;
        }
        .money {
          font-weight: bold;
          font-size: 14px;
          color: #e93323;
          span {
            font-size: 18px;
          }
        }
        .sales {
          font-size: 12px;
          color: #888888;
        }
      }
    }
  }
  .el-pagination {
    padding: 0;
    border: 1px solid #cccccc;
  }
  .nothing {
    margin-top: 100px;
    font-size: 16px;
    text-align: center;
    color: #999999;
    img {
      margin: 0 auto;
    }
  }
}
.detail-wrapper .el-pagination ::v-deep button {
  width: 78px;
  height: 38px;
  padding: 0;
  font-size: 15px;
  color: #707070;
}
.detail-wrapper .el-pagination ::v-deep button.btn-prev {
  border-right: 1px solid #cccccc;
}
.detail-wrapper .el-pagination ::v-deep button.btn-next {
  border-left: 1px solid #cccccc;
}
.detail-wrapper .el-pagination ::v-deep li{
  width: 38px;
  height: 38px;
  padding: 0;
  font-weight: normal;
  font-size: 15px;
  line-height: 38px;
  color: #707070;
}
.detail-wrapper .el-pagination ::v-deep li~li{
  border-left: 1px solid #cccccc;
}
.detail-wrapper .el-pagination ::v-deep li.active {
  background-color: #e93323;
  color: #ffffff;
}
.detail-wrapper .qrcode-button .qrcode ::v-deep img{
  display: block;
  width: 100%;
  height: 100%;
}
.detail-wrapper .detail-bd .detail-html ::v-deep div {
  width: 100% !important;
}
.detail-wrapper .detail-bd .detail-html ::v-deep img {
  display: block;
  width: 100% !important;
}
.store-favorites{
  margin-top: 14px;
  .collection{
    width: 80px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #333333;
    border: 1px solid #C8C8C8;
    border-radius: 2px;
    background: #fff;
    &.care{
      color: #fff;
      background-color: #e93323;
      border-color: #e93323;
    }
  }
}
</style>
