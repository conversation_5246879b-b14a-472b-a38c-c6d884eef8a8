# 服务包功能测试指南

## 测试环境准备

### 前端商城测试（pc-src项目）
1. 确保项目正常运行
2. 准备测试商品数据（包含服务包规格的商品）
3. 测试浏览器：Chrome、Firefox、Safari

### 后台管理测试（mer项目）
1. 确保后台管理系统正常运行
2. 登录管理员账号
3. 进入商品管理 → 添加商品页面

## 后台功能测试

### 1. 规格类型选择测试
**测试步骤：**
1. 进入商品管理 → 添加商品 → 规格设置
2. 查看规格类型选项
3. 分别选择"单规格"、"多规格"、"服务包模式"

**预期结果：**
- 显示三个规格类型选项
- 选择不同类型时界面正确切换
- 原有的单规格和多规格功能不受影响

### 2. 基础服务包配置测试
**测试步骤：**
1. 选择"服务包模式"
2. 填写基础配置信息：
   - 包名称：测试基础包
   - 价格：100
   - 交付时间：3天
   - 修改次数：2次
   - 包描述：这是一个测试服务包

**预期结果：**
- 显示基础配置界面
- 所有字段可正常输入
- 显示"Create Packages"按钮

### 3. 高级三列配置测试
**测试步骤：**
1. 点击"Create Packages"按钮
2. 配置Basic套餐：
   - 启用开关：开启
   - 包名称：基础版
   - 价格：50
   - 交付时间：3天
   - 修改次数：2次
3. 配置Standard套餐：
   - 启用开关：开启
   - 包名称：标准版
   - 价格：100
   - 交付时间：5天
   - 修改次数：5次
4. 配置Premium套餐：
   - 启用开关：开启
   - 包名称：高级版
   - 价格：200
   - 交付时间：7天
   - 修改次数：无限

**预期结果：**
- 界面展开为三列布局
- 每个套餐可独立配置
- 开关控制套餐启用状态
- 不同套餐显示不同主题色

### 4. 额外服务配置测试
**测试步骤：**
1. 配置额外快速交付：
   - 启用开关：开启
   - 服务名称：加急处理
   - 额外价格：30
   - 选择选项：1天之内、2天之内
2. 配置追加修改次数：
   - 启用开关：开启
   - 服务名称：额外修改
   - 额外价格：20
   - 选择选项：1次修改、2次修改
3. 配置服务保障期：
   - 启用开关：开启
   - 服务名称：延长保障
   - 额外价格：50
   - 选择选项：3个月、半年

**预期结果：**
- 额外服务配置正确显示
- 开关控制服务启用状态
- 复选框选项可正常选择

### 5. 规格生成测试
**测试步骤：**
1. 完成所有配置后点击"生成服务包规格"
2. 观察系统反馈
3. 点击"预览配置"查看生成的数据

**预期结果：**
- 显示"服务包规格生成成功！"提示
- 预览窗口显示JSON格式的配置数据
- 数据结构符合预期格式

### 6. 模式切换测试
**测试步骤：**
1. 在高级模式下点击"返回基础模式"
2. 再次点击"Create Packages"
3. 检查配置是否保持

**预期结果：**
- 模式切换正常
- 已配置的数据不丢失
- 界面状态正确恢复

## 前端商城测试

### 1. 商品详情页显示测试
**测试步骤：**
1. 创建一个包含服务包规格的商品
2. 在前端商城访问该商品详情页
3. 检查规格展示区域

**预期结果：**
- 自动识别为服务包商品
- 显示Fiverr风格的服务包选择界面
- 不显示传统的规格选择界面

### 2. 服务包选择测试
**测试步骤：**
1. 点击不同的服务包选项（Basic、Standard、Premium）
2. 观察界面变化和价格更新
3. 检查选中状态的视觉反馈

**预期结果：**
- 服务包选项可正常切换
- 选中状态有明显的视觉反馈
- 价格实时更新

### 3. 额外服务选择测试
**测试步骤：**
1. 选择额外服务选项
2. 尝试选择超过2个额外服务
3. 观察价格计算

**预期结果：**
- 额外服务可正常选择
- 最多只能选择2个额外服务
- 超出限制的选项自动禁用
- 总价正确计算（基础价格 + 额外服务价格）

### 4. 购物车功能测试
**测试步骤：**
1. 选择服务包和额外服务
2. 点击"加入购物车"
3. 点击"立即购买"
4. 检查购物车和订单页面

**预期结果：**
- 购物车正确记录服务包选择
- 价格计算准确
- 订单流程正常

### 5. 缓存功能测试
**测试步骤：**
1. 选择服务包和额外服务
2. 刷新页面或返回重新进入
3. 检查选择状态是否保持

**预期结果：**
- 用户选择的状态得到保持
- 缓存机制正常工作
- 不影响原有的规格缓存功能

### 6. 传统商品兼容性测试
**测试步骤：**
1. 访问传统规格商品的详情页
2. 检查规格选择功能
3. 测试购买流程

**预期结果：**
- 传统商品显示原有的规格选择界面
- 所有原有功能正常工作
- 不受服务包功能影响

## 兼容性测试

### 1. 浏览器兼容性
**测试浏览器：**
- Chrome（最新版本）
- Firefox（最新版本）
- Safari（最新版本）
- Edge（最新版本）

**测试内容：**
- 界面显示正常
- 交互功能正常
- 样式渲染正确

### 2. 响应式测试
**测试设备：**
- 桌面端（1920x1080）
- 平板端（768x1024）
- 手机端（375x667）

**测试内容：**
- 布局自适应
- 按钮可点击
- 文字清晰可读

### 3. 数据兼容性测试
**测试场景：**
- 新建服务包商品
- 编辑现有传统商品
- 复制商品功能
- 批量操作

**预期结果：**
- 数据结构兼容
- 不影响现有数据
- 功能正常工作

## 性能测试

### 1. 页面加载性能
- 商品详情页加载时间
- 规格切换响应时间
- 价格计算速度

### 2. 内存使用
- 长时间使用后的内存占用
- 页面切换时的内存释放

## 错误处理测试

### 1. 异常输入测试
- 价格输入负数
- 必填字段为空
- 特殊字符输入

### 2. 网络异常测试
- 网络中断时的处理
- 接口调用失败的处理

## 测试报告模板

### 测试结果记录
```
测试项目：[具体测试项目]
测试时间：[测试日期]
测试环境：[浏览器/设备信息]
测试结果：[通过/失败]
问题描述：[如有问题，详细描述]
修复建议：[修复建议]
```

### 总体评估
- 功能完整性：✓/✗
- 界面友好性：✓/✗
- 性能表现：✓/✗
- 兼容性：✓/✗
- 稳定性：✓/✗

通过这个全面的测试指南，可以确保服务包功能的质量和稳定性，同时验证与现有系统的兼容性。
