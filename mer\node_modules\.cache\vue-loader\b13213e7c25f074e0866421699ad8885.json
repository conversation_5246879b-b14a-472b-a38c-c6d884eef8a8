{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue?vue&type=template&id=f7b5f20a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue", "mtime": 1750422426707}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div>\n  <el-row>\n    <el-col :span=\"24\">\n      <el-form-item :label=\"$t('规格类型：')\" props=\"spec_type\">\n        <el-radio-group v-model=\"formValidate.spec_type\">\n          <el-radio :label=\"0\" class=\"radio\">{{ $t('单规格') }}</el-radio>\n          <el-radio :label=\"1\">{{ $t('多规格') }}</el-radio>\n          <el-radio :label=\"2\">{{ $t('服务包模式') }}</el-radio>\n        </el-radio-group>\n        <el-dropdown\n          v-if=\"formValidate.spec_type == 1 && ruleList.length > 0\"\n          class=\"ml20\"\n          @command=\"confirm\"\n          trigger=\"hover\"\n        >\n          <span class=\"el-dropdown-link\">{{ $t('选择规格模板') }}<i class=\"el-icon-arrow-down el-icon--right\"></i\n          ></span>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-scrollbar style=\"max-height: 300px;overflow-y:scroll;\">\n              <el-dropdown-item\n                v-for=\"(item, index) in ruleList\"\n                :key=\"index\"\n                :command=\"item.attr_template_id\"\n              >\n                {{ item.template_name }}\n              </el-dropdown-item>\n            </el-scrollbar>\n          </el-dropdown-menu>\n        </el-dropdown>\n      </el-form-item>\n    </el-col>\n    <!-- 规格设置 -->\n    <el-col :span=\"24\" v-if=\"formValidate.spec_type === 1\" class=\"noForm\">\n      <el-form-item :label=\"$t('商品规格：')\" required>\n        <div class=\"specifications\">\n          <draggable\n            group=\"specifications\"\n            :disabled=\"attrs.length < 2\"\n            :list=\"attrs\"\n            handle=\".move-icon\"\n            @end=\"onMoveSpec\"\n            animation=\"300\"\n          >\n            <div\n              class=\"specifications-item active\"\n              v-for=\"(item, index) in attrs\"\n              :key=\"index\"\n              @click=\"changeCurrentIndex(index)\"\n            >\n              <div class=\"move-icon\">\n                <span class=\"iconfont icondrag2\"></span>\n              </div>\n              <i\n                class=\"del el-icon-error\"\n                @click=\"handleRemoveRole(index, item.value)\"\n              ></i>\n              <div class=\"specifications-item-box\">\n                <div class=\"lineBox\"></div>\n                <div class=\"specifications-item-name mb18\">\n                  <el-input\n                    size=\"small\"\n                    v-model=\"item.value\"\n                    :placeholder=\"$t('规格名称')\"\n                    @change=\"attrChangeValue(index, item.value)\"\n                    @focus=\"handleFocus(item.value)\"\n                    class=\"specifications-item-name-input\"\n                    maxlength=\"30\"\n                    show-word-limit\n                  ></el-input>\n                  <el-checkbox\n                    class=\"ml20\"\n                    v-model=\"item.add_pic\"\n                    :disabled=\"!item.add_pic && !canSel\"\n                    :true-label=\"1\"\n                    :false-label=\"0\"\n                    @change=\"e => addPic(e, index)\"\n                    >{{ $t('添加规格图') }}</el-checkbox\n                  >\n                  <el-tooltip\n                    class=\"item\"\n                    effect=\"dark\"\n                    :content=\"$t('添加规格图片, 仅支持打开一个(建议尺寸:800*800)')\"\n                    placement=\"right\"\n                  >\n                    <i class=\"el-icon-info\"></i>\n                  </el-tooltip>\n                </div>\n                <div class=\"rulesBox ml30\">\n                  <draggable\n                    class=\"item\"\n                    :list=\"item.detail\"\n                    :disabled=\"item.detail.length < 2\"\n                    handle=\".drag\"\n                    @end=\"onMoveSpec\"\n                  >\n                    <div\n                      v-for=\"(det, indexn) in item.detail\"\n                      :key=\"indexn\"\n                      class=\"mr10 spec drag\"\n                    >\n                      <i\n                        class=\"el-icon-error\"\n                        @click=\"handleRemove2(item.detail, indexn, det.value)\"\n                      ></i>\n                      <el-input\n                        style=\"width:120px;\"\n                        size=\"small\"\n                        v-model=\"det.value\"\n                        :placeholder=\"$t('规格值')\"\n                        @change=\"attrDetailChangeValue(det.value, index)\"\n                        @focus=\"handleFocus(det.value)\"\n                        maxlength=\"30\"\n                        @blur=\"handleBlur()\"\n                      >\n                        <template slot=\"prefix\">\n                          <span class=\"iconfont icondrag2\"></span>\n                        </template>\n                      </el-input>\n                      <div class=\"img-popover\" v-if=\"item.add_pic\">\n                        <div class=\"popper-arrow\"></div>\n                        <div\n                          class=\"popper\"\n                          @click=\"handleSelImg(det, index, indexn)\"\n                        >\n                          <img class=\"img\" v-if=\"det.pic\" :src=\"det.pic\" />\n                          <i v-else class=\"el-icon-plus\"></i>\n                        </div>\n                        <i\n                          v-if=\"det.pic\"\n                          class=\"img-del el-icon-error\"\n                          @click=\"handleRemoveImg(det, index, indexn)\"\n                        ></i>\n                      </div>\n                    </div>\n                    <el-popover\n                      :ref=\"'popoverRef_' + index\"\n                      placement=\"\"\n                      width=\"210\"\n                      trigger=\"click\"\n                      @after-enter=\"handleShowPop(index)\"\n                    >\n                      <el-input\n                        style=\"min-width:80px;;width:210;\"\n                        :ref=\"'inputRef_' + index\"\n                        size=\"small\"\n                        :placeholder=\"$t('请输入规格值')\"\n                        v-model=\"formDynamic.attrsVal\"\n                        @keyup.enter.native=\"\n                          createAttr(formDynamic.attrsVal, index)\n                        \"\n                        @blur=\"createAttr(formDynamic.attrsVal, index)\"\n                        maxlength=\"30\"\n                        show-word-limit\n                      >\n                      </el-input>\n                      <div class=\"addfont\" slot=\"reference\">{{ $t('添加规格值') }}</div>\n                    </el-popover>\n                  </draggable>\n                </div>\n              </div>\n            </div>\n          </draggable>\n          <el-button\n            v-if=\"attrs.length < 4\"\n            size=\"small\"\n            type=\"text\"\n            @click=\"handleAddRole()\"\n            >{{ $t('添加新规格') }}</el-button\n          >\n          <el-button\n            v-if=\"attrs.length >= 1\"\n            size=\"small\"\n            type=\"text\"\n            @click=\"handleSaveAsTemplate()\"\n            >{{ $t('另存为模板') }}</el-button\n          >\n        </div>\n      </el-form-item>\n    </el-col>\n\n    <!-- 服务包配置 -->\n    <el-col :span=\"24\" v-if=\"formValidate.spec_type === 2\" class=\"noForm\">\n      <el-form-item :label=\"$t('服务包配置：')\" required>\n        <div class=\"service-package-config\">\n          <!-- 基础配置模式 -->\n          <div v-if=\"!showAdvancedPackages\" class=\"basic-config\">\n            <el-card class=\"basic-package-card\">\n              <div slot=\"header\" class=\"card-header\">\n                <span>基础服务包配置</span>\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"expandToAdvancedPackages\"\n                >\n                  Create Packages\n                </el-button>\n              </div>\n              <el-row :gutter=\"20\">\n                <el-col :span=\"12\">\n                  <el-form-item label=\"包名称\">\n                    <el-input\n                      v-model=\"packageConfig.basic.name\"\n                      placeholder=\"输入服务包名称\"\n                    />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"价格\">\n                    <el-input-number\n                      v-model=\"packageConfig.basic.price\"\n                      :min=\"0\"\n                      controls-position=\"right\"\n                    />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"交付时间\">\n                    <el-select v-model=\"packageConfig.basic.delivery_time\" placeholder=\"选择交付时间\">\n                      <el-option label=\"1天\" value=\"1天\"></el-option>\n                      <el-option label=\"2天\" value=\"2天\"></el-option>\n                      <el-option label=\"3天\" value=\"3天\"></el-option>\n                      <el-option label=\"4天\" value=\"4天\"></el-option>\n                      <el-option label=\"5天\" value=\"5天\"></el-option>\n                      <el-option label=\"7天\" value=\"7天\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"修改次数\">\n                    <el-select v-model=\"packageConfig.basic.revisions\" placeholder=\"选择修改次数\">\n                      <el-option label=\"1次\" value=\"1\"></el-option>\n                      <el-option label=\"2次\" value=\"2\"></el-option>\n                      <el-option label=\"3次\" value=\"3\"></el-option>\n                      <el-option label=\"5次\" value=\"5\"></el-option>\n                      <el-option label=\"无限次\" value=\"无限\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"包描述\">\n                    <el-input\n                      type=\"textarea\"\n                      v-model=\"packageConfig.basic.description\"\n                      placeholder=\"描述服务包的详细内容\"\n                      :rows=\"3\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-card>\n          </div>\n\n          <!-- 高级三列配置模式 -->\n          <div v-else class=\"advanced-packages\">\n            <div class=\"packages-header\">\n              <h3>服务包配置</h3>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"collapseToBasicPackage\"\n              >\n                返回基础模式\n              </el-button>\n            </div>\n            <el-row :gutter=\"20\" class=\"packages-row\">\n              <!-- Basic 套餐 -->\n              <el-col :span=\"8\">\n                <el-card class=\"package-card basic-card\">\n                  <div slot=\"header\" class=\"package-header\">\n                    <span class=\"package-title\">Basic</span>\n                    <el-switch v-model=\"packageConfig.basic.enabled\" />\n                  </div>\n                  <div v-if=\"packageConfig.basic.enabled\">\n                    <el-form-item label=\"包名称\" size=\"small\">\n                      <el-input v-model=\"packageConfig.basic.name\" placeholder=\"Basic套餐名称\" />\n                    </el-form-item>\n                    <el-form-item label=\"价格\" size=\"small\">\n                      <el-input-number\n                        v-model=\"packageConfig.basic.price\"\n                        :min=\"0\"\n                        controls-position=\"right\"\n                        style=\"width: 100%\"\n                      />\n                    </el-form-item>\n                    <el-form-item label=\"交付时间\" size=\"small\">\n                      <el-select v-model=\"packageConfig.basic.delivery_time\" style=\"width: 100%\">\n                        <el-option label=\"1天\" value=\"1天\"></el-option>\n                        <el-option label=\"2天\" value=\"2天\"></el-option>\n                        <el-option label=\"3天\" value=\"3天\"></el-option>\n                        <el-option label=\"4天\" value=\"4天\"></el-option>\n                        <el-option label=\"5天\" value=\"5天\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <el-form-item label=\"修改次数\" size=\"small\">\n                      <el-select v-model=\"packageConfig.basic.revisions\" style=\"width: 100%\">\n                        <el-option label=\"1次\" value=\"1\"></el-option>\n                        <el-option label=\"2次\" value=\"2\"></el-option>\n                        <el-option label=\"3次\" value=\"3\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <el-form-item label=\"描述\" size=\"small\">\n                      <el-input\n                        type=\"textarea\"\n                        v-model=\"packageConfig.basic.description\"\n                        :rows=\"2\"\n                      />\n                    </el-form-item>\n                  </div>\n                </el-card>\n              </el-col>\n\n              <!-- Standard 套餐 -->\n              <el-col :span=\"8\">\n                <el-card class=\"package-card standard-card\">\n                  <div slot=\"header\" class=\"package-header\">\n                    <span class=\"package-title\">Standard</span>\n                    <el-switch v-model=\"packageConfig.standard.enabled\" />\n                  </div>\n                  <div v-if=\"packageConfig.standard.enabled\">\n                    <el-form-item label=\"包名称\" size=\"small\">\n                      <el-input v-model=\"packageConfig.standard.name\" placeholder=\"Standard套餐名称\" />\n                    </el-form-item>\n                    <el-form-item label=\"价格\" size=\"small\">\n                      <el-input-number\n                        v-model=\"packageConfig.standard.price\"\n                        :min=\"0\"\n                        controls-position=\"right\"\n                        style=\"width: 100%\"\n                      />\n                    </el-form-item>\n                    <el-form-item label=\"交付时间\" size=\"small\">\n                      <el-select v-model=\"packageConfig.standard.delivery_time\" style=\"width: 100%\">\n                        <el-option label=\"3天\" value=\"3天\"></el-option>\n                        <el-option label=\"5天\" value=\"5天\"></el-option>\n                        <el-option label=\"7天\" value=\"7天\"></el-option>\n                        <el-option label=\"10天\" value=\"10天\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <el-form-item label=\"修改次数\" size=\"small\">\n                      <el-select v-model=\"packageConfig.standard.revisions\" style=\"width: 100%\">\n                        <el-option label=\"3次\" value=\"3\"></el-option>\n                        <el-option label=\"5次\" value=\"5\"></el-option>\n                        <el-option label=\"10次\" value=\"10\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <el-form-item label=\"描述\" size=\"small\">\n                      <el-input\n                        type=\"textarea\"\n                        v-model=\"packageConfig.standard.description\"\n                        :rows=\"2\"\n                      />\n                    </el-form-item>\n                  </div>\n                </el-card>\n              </el-col>\n\n              <!-- Premium 套餐 -->\n              <el-col :span=\"8\">\n                <el-card class=\"package-card premium-card\">\n                  <div slot=\"header\" class=\"package-header\">\n                    <span class=\"package-title\">Premium</span>\n                    <el-switch v-model=\"packageConfig.premium.enabled\" />\n                  </div>\n                  <div v-if=\"packageConfig.premium.enabled\">\n                    <el-form-item label=\"包名称\" size=\"small\">\n                      <el-input v-model=\"packageConfig.premium.name\" placeholder=\"Premium套餐名称\" />\n                    </el-form-item>\n                    <el-form-item label=\"价格\" size=\"small\">\n                      <el-input-number\n                        v-model=\"packageConfig.premium.price\"\n                        :min=\"0\"\n                        controls-position=\"right\"\n                        style=\"width: 100%\"\n                      />\n                    </el-form-item>\n                    <el-form-item label=\"交付时间\" size=\"small\">\n                      <el-select v-model=\"packageConfig.premium.delivery_time\" style=\"width: 100%\">\n                        <el-option label=\"5天\" value=\"5天\"></el-option>\n                        <el-option label=\"7天\" value=\"7天\"></el-option>\n                        <el-option label=\"10天\" value=\"10天\"></el-option>\n                        <el-option label=\"15天\" value=\"15天\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <el-form-item label=\"修改次数\" size=\"small\">\n                      <el-select v-model=\"packageConfig.premium.revisions\" style=\"width: 100%\">\n                        <el-option label=\"5次\" value=\"5\"></el-option>\n                        <el-option label=\"10次\" value=\"10\"></el-option>\n                        <el-option label=\"无限次\" value=\"无限\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <el-form-item label=\"描述\" size=\"small\">\n                      <el-input\n                        type=\"textarea\"\n                        v-model=\"packageConfig.premium.description\"\n                        :rows=\"2\"\n                      />\n                    </el-form-item>\n                  </div>\n                </el-card>\n              </el-col>\n            </el-row>\n          </div>\n\n          <!-- 额外服务配置 -->\n          <div v-if=\"showAdvancedPackages\" class=\"extra-services-config\">\n            <el-divider content-position=\"left\">额外服务配置（最多2个大类）</el-divider>\n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-card class=\"extra-service-card\">\n                  <div slot=\"header\" class=\"card-header\">\n                    <span>额外快速交付时间</span>\n                    <el-switch v-model=\"extraServices.fastDelivery.enabled\" />\n                  </div>\n                  <div v-if=\"extraServices.fastDelivery.enabled\">\n                    <el-form-item label=\"服务名称\" size=\"small\">\n                      <el-input v-model=\"extraServices.fastDelivery.name\" placeholder=\"如：加急处理\" />\n                    </el-form-item>\n                    <el-form-item label=\"额外价格\" size=\"small\">\n                      <el-input-number\n                        v-model=\"extraServices.fastDelivery.price\"\n                        :min=\"0\"\n                        controls-position=\"right\"\n                        style=\"width: 100%\"\n                      />\n                    </el-form-item>\n                    <el-form-item label=\"交付时间选项\" size=\"small\">\n                      <el-checkbox-group v-model=\"extraServices.fastDelivery.options\">\n                        <el-checkbox label=\"1天之内\">1天之内</el-checkbox>\n                        <el-checkbox label=\"2天之内\">2天之内</el-checkbox>\n                        <el-checkbox label=\"3天之内\">3天之内</el-checkbox>\n                      </el-checkbox-group>\n                    </el-form-item>\n                  </div>\n                </el-card>\n              </el-col>\n\n              <el-col :span=\"12\">\n                <el-card class=\"extra-service-card\">\n                  <div slot=\"header\" class=\"card-header\">\n                    <span>追加修改次数</span>\n                    <el-switch v-model=\"extraServices.additionalRevisions.enabled\" />\n                  </div>\n                  <div v-if=\"extraServices.additionalRevisions.enabled\">\n                    <el-form-item label=\"服务名称\" size=\"small\">\n                      <el-input v-model=\"extraServices.additionalRevisions.name\" placeholder=\"如：额外修改\" />\n                    </el-form-item>\n                    <el-form-item label=\"额外价格\" size=\"small\">\n                      <el-input-number\n                        v-model=\"extraServices.additionalRevisions.price\"\n                        :min=\"0\"\n                        controls-position=\"right\"\n                        style=\"width: 100%\"\n                      />\n                    </el-form-item>\n                    <el-form-item label=\"修改次数选项\" size=\"small\">\n                      <el-checkbox-group v-model=\"extraServices.additionalRevisions.options\">\n                        <el-checkbox label=\"1次修改\">1次修改</el-checkbox>\n                        <el-checkbox label=\"2次修改\">2次修改</el-checkbox>\n                        <el-checkbox label=\"3次修改\">3次修改</el-checkbox>\n                        <el-checkbox label=\"5次修改\">5次修改</el-checkbox>\n                      </el-checkbox-group>\n                    </el-form-item>\n                  </div>\n                </el-card>\n              </el-col>\n            </el-row>\n\n            <!-- 保过期服务 -->\n            <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n              <el-col :span=\"12\">\n                <el-card class=\"extra-service-card\">\n                  <div slot=\"header\" class=\"card-header\">\n                    <span>服务保障期</span>\n                    <el-switch v-model=\"extraServices.warranty.enabled\" />\n                  </div>\n                  <div v-if=\"extraServices.warranty.enabled\">\n                    <el-form-item label=\"服务名称\" size=\"small\">\n                      <el-input v-model=\"extraServices.warranty.name\" placeholder=\"如：延长保障\" />\n                    </el-form-item>\n                    <el-form-item label=\"额外价格\" size=\"small\">\n                      <el-input-number\n                        v-model=\"extraServices.warranty.price\"\n                        :min=\"0\"\n                        controls-position=\"right\"\n                        style=\"width: 100%\"\n                      />\n                    </el-form-item>\n                    <el-form-item label=\"保障期选项\" size=\"small\">\n                      <el-checkbox-group v-model=\"extraServices.warranty.options\">\n                        <el-checkbox label=\"3个月\">3个月</el-checkbox>\n                        <el-checkbox label=\"半年\">半年</el-checkbox>\n                        <el-checkbox label=\"一年\">一年</el-checkbox>\n                      </el-checkbox-group>\n                    </el-form-item>\n                  </div>\n                </el-card>\n              </el-col>\n            </el-row>\n\n            <!-- 生成规格按钮 -->\n            <div class=\"generate-specs-btn\" style=\"margin-top: 20px; text-align: center;\">\n              <el-button type=\"primary\" @click=\"generateServicePackageSpecs\">\n                生成服务包规格\n              </el-button>\n              <el-button @click=\"previewServicePackage\">\n                预览配置\n              </el-button>\n            </div>\n          </div>\n        </div>\n      </el-form-item>\n    </el-col>\n\n    <!-- 批量设置-->\n    <el-col :xl=\"24\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\n      <!-- 单规格表格-->\n      <el-form-item v-if=\"formValidate.spec_type === 0\">\n        <el-table :data=\"OneattrValue\" border class=\"tabNumWidth\" size=\"mini\">\n          <el-table-column align=\"center\" :label=\"$t('图片')\" min-width=\"80\">\n            <template slot-scope=\"scope\">\n              <div\n                class=\"upLoadPicBox specPictrue\"\n                @click=\"modalPicTap('1', 'dan')\"\n              >\n                <div v-if=\"formValidate.image\" class=\"pictrue tabPic\">\n                  <img :src=\"scope.row.image\" />\n                </div>\n                <div v-else class=\"upLoad tabPic\">\n                  <i class=\"el-icon-camera cameraIconfont\" />\n                </div>\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column\n            v-for=\"(item, iii) in attrValue\"\n            :key=\"iii\"\n            :label=\"formThead[iii] && formThead[iii].title\"\n            align=\"center\"\n            min-width=\"110\"\n          >\n            <template slot-scope=\"scope\">\n              <div v-if=\"formValidate.svip_price_type != 0 && formThead[iii]\">\n                <el-input\n                  v-if=\"formThead[iii].title === $t('规格编码')\"\n                  v-model=\"scope.row[iii]\"\n                  type=\"text\"\n                  class=\"priceBox\"\n                />\n                <el-input\n                  v-if=\"formThead[iii].title === $t('条形码')\"\n                  v-model=\"scope.row[iii]\"\n                  type=\"text\"\n                  class=\"priceBox\"\n                />\n                <el-input-number\n                  v-if=\"\n                    formThead[iii].title !== '付费会员价' &&\n                      formThead[iii].title !== '规格编码' &&\n                      formThead[iii].title !== '条形码' &&\n                      formThead[iii].title !== $t('库存')\n                  \"\n                  v-model=\"scope.row[iii]\"\n                  :min=\"0\"\n                  size=\"small\"\n                  @blur=\"memberPrice(formThead[iii], scope.row)\"\n                  class=\"priceBox\"\n                  controls-position=\"right\"\n                />\n                <el-input\n                  v-if=\"\n                    formThead[iii].title === $t('库存') &&\n                      (formValidate.type == 2 || formValidate.type == 3)\n                  \"\n                  v-model=\"scope.row[iii]\"\n                  type=\"text\"\n                  size=\"small\"\n                  class=\"priceBox\"\n                  disabled\n                />\n                <el-input\n                  v-else-if=\"\n                    formThead[iii].title === $t('库存') &&\n                      (formValidate.type !== 2 && formValidate.type !== 3)\n                  \"\n                  v-model=\"scope.row[iii]\"\n                  type=\"text\"\n                  size=\"small\"\n                  class=\"priceBox\"\n                />\n              </div>\n              <div v-else>\n                <el-input\n                  v-if=\"formThead[iii].title === $t('规格编码')\"\n                  v-model=\"scope.row[iii]\"\n                  type=\"text\"\n                  size=\"small\"\n                  class=\"priceBox\"\n                />\n                <el-input\n                  v-else-if=\"formThead[iii].title === $t('条形码')\"\n                  v-model=\"scope.row[iii]\"\n                  type=\"text\"\n                  size=\"small\"\n                  class=\"priceBox\"\n                />\n                <el-input-number\n                  v-else\n                  v-model=\"scope.row[iii]\"\n                  :min=\"0\"\n                  size=\"small\"\n                  :disabled=\"\n                    formThead[iii].title === '库存' &&\n                      (formValidate.type == 2 || formValidate.type == 3)\n                  \"\n                  class=\"priceBox\"\n                  controls-position=\"right\"\n                />\n              </div>\n            </template>\n          </el-table-column>\n          <template v-if=\"formValidate.type == 2\">\n            <el-table-column align=\"center\" :label=\"$t('云盘设置')\" min-width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button\n                  v-if=\"\n                    scope.row.cdkey &&\n                      !scope.row.cdkey.list &&\n                      !scope.row.stock\n                  \"\n                  size=\"small\"\n                  @click=\"addVirtual(0, 0, 'OneattrValue')\"\n                  >{{ $t('添加链接') }}</el-button\n                >\n                <span\n                  v-else\n                  class=\"seeCatMy\"\n                  @click=\"seeVirtual(0, OneattrValue[0], 'OneattrValue', 0)\"\n                  >{{ $t('已设置') }}</span\n                >\n              </template>\n            </el-table-column>\n          </template>\n          <template v-if=\"formValidate.type == 3\">\n            <el-table-column align=\"center\" :label=\"$t('卡密设置')\" min-width=\"140\">\n              <template slot-scope=\"scope\">\n                <el-select\n                  :placeholder=\"$t('请选择卡密库')\"\n                  clearable\n                  size=\"small\"\n                  v-model=\"scope.row.library_id\"\n                  @change=\"handleChange($event, scope.$index, 'OneattrValue')\"\n                >\n                  <el-option\n                    :value=\"item.id\"\n                    v-for=\"(item, index) in cdkeyLibraryList\"\n                    :key=\"index\"\n                    :label=\"item.name\"\n                    :disabled=\"\n                      !item.checkout &&\n                        (item.product_id != product_id ||\n                          (item.product_id == product_id &&\n                            $route.query.type == 'copy'))\n                    \"\n                  ></el-option>\n                </el-select>\n              </template>\n            </el-table-column>\n          </template>\n        </el-table>\n      </el-form-item>\n      <!-- 多规格表格-->\n      <el-form-item\n        v-if=\"formValidate.spec_type == 1\"\n        class=\"labeltop\"\n        :label=\"$t('规格列表：')\"\n      >\n        <el-table\n          :data=\"ManyAttrValue\"\n          style=\"width: 100%\"\n          :cell-class-name=\"tableCellClassName\"\n          :span-method=\"objectSpanMethod\"\n          border\n          :key=\"tableKey\"\n          size=\"small\"\n        >\n          <el-table-column\n            v-for=\"(item, index) in formValidate.header\"\n            :key=\"index\"\n            :label=\"item.title\"\n            :min-width=\"item.minWidth || '100'\"\n            :fixed=\"item.fixed\"\n          >\n            <template slot-scope=\"scope\">\n              <!-- 批量设置 -->\n              <template v-if=\"scope.$index == 0\">\n                <template v-if=\"item.key\">\n                  <div\n                    v-if=\"\n                      attrs.length &&\n                        attrs[scope.column.index] &&\n                        ManyAttrValue.length\n                    \"\n                  >\n                    <el-select\n                      v-model=\"oneFormBatch[0][item.title]\"\n                      :placeholder=\"`请选择${item.title}`\"\n                      size=\"small\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"val in attrs[scope.column.index].detail\"\n                        :key=\"val.value\"\n                        :label=\"val.value\"\n                        :value=\"val.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </div>\n                </template>\n                <template v-else-if=\"item.slot === 'image'\">\n                  <div\n                    class=\"upLoadPicBox specPictrue\"\n                    @click.stop=\"modalPicTap('1', 'pi', scope.$index)\"\n                  >\n                    <div class=\"upLoad tabPic\" v-if=\"oneFormBatch[0].image\">\n                      <img v-lazy=\"oneFormBatch[0].image\" />\n                      <i\n                        class=\"el-icon-error btndel btnclose\"\n                        @click.stop=\"oneFormBatch[0].image = ''\"\n                      />\n                    </div>\n                    <div class=\"upLoad tabPic\" v-else>\n                      <i class=\"el-icon-camera cameraIconfont\"></i>\n                    </div>\n                  </div>\n                </template>\n                <template v-else-if=\"item.slot === 'price'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"oneFormBatch[0].price\"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template v-else-if=\"item.slot === 'cost'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"oneFormBatch[0].cost\"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    clearable\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template v-else-if=\"item.slot === 'ot_price'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"oneFormBatch[0].ot_price\"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    clearable\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template v-else-if=\"item.slot === 'stock'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"oneFormBatch[0].stock\"\n                    :disabled=\"\n                      formValidate.type == 3 || formValidate.type == 2\n                    \"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    clearable\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template v-else-if=\"item.slot === 'fictitious'\">\n                  --\n                </template>\n                <template v-else-if=\"item.slot === 'bar_code'\">\n                  <el-input v-model=\"oneFormBatch[0].bar_code\"></el-input>\n                </template>\n                <template v-else-if=\"item.slot === 'bar_code_number'\">\n                  <el-input\n                    v-model=\"oneFormBatch[0].bar_code_number\"\n                  ></el-input>\n                </template>\n                <template v-else-if=\"item.slot === 'weight'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"oneFormBatch[0].weight\"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    clearable\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template v-else-if=\"item.slot === 'volume'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"oneFormBatch[0].volume\"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    clearable\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template v-else-if=\"item.slot === 'selected_spec'\">\n                  --\n                </template>\n                <template v-else-if=\"item.slot === 'action'\">\n                  <el-button type=\"text\" size=\"mini\" @click=\"batchAdd\"\n                    >{{ $t('批量修改') }}</el-button\n                  >\n                  <el-button type=\"text\" size=\"mini\" @click=\"batchDel\"\n                    >{{ $t('清空') }}</el-button\n                  >\n                </template>\n              </template>\n              <template v-else>\n                <template v-if=\"item.key\">\n                  <div>\n                    <span>{{ scope.row.detail[item.key] }}</span>\n                  </div>\n                </template>\n                <template v-if=\"item.slot === 'image'\">\n                  <div\n                    class=\"upLoadPicBox specPictrue\"\n                    @click=\"modalPicTap('1', 'duo', scope.$index)\"\n                  >\n                    <div\n                      class=\"upLoad tabPic\"\n                      v-if=\"scope.row.image || scope.row.pic\"\n                    >\n                      <img :src=\"scope.row.image || scope.row.pic\" />\n                    </div>\n                    <div class=\"upLoad tabPic\" v-else>\n                      <i\n                        class=\"el-icon-camera cameraIconfont\"\n                        style=\"font-size: 24px\"\n                      ></i>\n                    </div>\n                  </div>\n                </template>\n                <template v-if=\"item.slot === 'price'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"ManyAttrValue[scope.$index].price\"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template v-else-if=\"item.slot === 'cost'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"ManyAttrValue[scope.$index].cost\"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template v-else-if=\"item.slot === 'ot_price'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"ManyAttrValue[scope.$index].ot_price\"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template v-else-if=\"item.slot === 'stock'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"ManyAttrValue[scope.$index].stock\"\n                    :disabled=\"\n                      formValidate.type == 3 || formValidate.type == 2\n                    \"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    :precision=\"0\"\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template v-else-if=\"item.slot === 'bar_code'\">\n                  <el-input\n                    v-model=\"ManyAttrValue[scope.$index].bar_code\"\n                  ></el-input>\n                </template>\n                <template v-else-if=\"item.slot === 'bar_code_number'\">\n                  <el-input\n                    v-model=\"ManyAttrValue[scope.$index].bar_code_number\"\n                  ></el-input>\n                </template>\n                <template v-else-if=\"item.slot === 'weight'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"ManyAttrValue[scope.$index].weight\"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template v-else-if=\"item.slot === 'volume'\">\n                  <el-input-number\n                    :controls=\"false\"\n                    v-model=\"ManyAttrValue[scope.$index].volume\"\n                    :min=\"0\"\n                    :max=\"9999999999\"\n                    class=\"priceBox\"\n                  ></el-input-number>\n                </template>\n                <template\n                  v-else-if=\"\n                    item.slot === 'fictitious' && formValidate.type == 2\n                  \"\n                >\n                  <el-button\n                    v-if=\"\n                      !scope.row.cdkey ||\n                        (scope.row.cdkey &&\n                          !scope.row.cdkey.list &&\n                          !scope.row.stock)\n                    \"\n                    size=\"small\"\n                    @click=\"addVirtual(0, scope.$index, 'ManyAttrValue')\"\n                    >{{ $t('添加链接') }}</el-button\n                  >\n                  <span\n                    v-else\n                    class=\"seeCatMy\"\n                    @click=\"\n                      seeVirtual(\n                        0,\n                        ManyAttrValue[scope.$index],\n                        'ManyAttrValue',\n                        scope.$index\n                      )\n                    \"\n                    >{{ $t('已设置') }}</span\n                  >\n                </template>\n                <template\n                  v-else-if=\"\n                    item.slot === 'fictitious' && formValidate.type == 3\n                  \"\n                  slot-scope=\"scope\"\n                >\n                  <el-select\n                    clearable\n                    :placeholder=\"$t('请选择卡密库')\"\n                    size=\"small\"\n                    v-model=\"scope.row.library_id\"\n                    @change=\"\n                      handleChange($event, scope.$index, 'ManyAttrValue')\n                    \"\n                    @visible-change=\"\n                      getSelectedLiarbry(\n                        ManyAttrValue[scope.$index],\n                        ManyAttrValue\n                      )\n                    \"\n                  >\n                    <el-option\n                      :value=\"item.id\"\n                      v-for=\"(item, index) in cdkeyLibraryList\"\n                      :key=\"index\"\n                      :label=\"item.name\"\n                      :disabled=\"\n                        (!item.checkout &&\n                          (item.product_id != product_id ||\n                            (item.product_id == product_id &&\n                              $route.query.type == 'copy'))) ||\n                          (selectedLibrary.length > 0 &&\n                            selectedLibrary.indexOf(item.id) != -1)\n                      \"\n                    ></el-option>\n                  </el-select>\n                </template>\n                <template v-else-if=\"item.slot === 'selected_spec'\">\n                  <el-switch\n                    v-model=\"ManyAttrValue[scope.$index].is_default_select\"\n                    :active-value=\"1\"\n                    :inactive-value=\"0\"\n                    @change=\"e => changeDefaultSelect(e, scope.$index)\"\n                  />\n                </template>\n                <template v-else-if=\"item.slot === 'action'\">\n                  <el-switch\n                    class=\"defineSwitch\"\n                    v-model=\"ManyAttrValue[scope.$index].is_show\"\n                    active-text=\"显示\"\n                    inactive-text=\"隐藏\"\n                    :active-value=\"1\"\n                    :inactive-value=\"0\"\n                    @change=\"changeDefaultShow(scope.$index)\"\n                  />\n                </template>\n              </template>\n            </template>\n          </el-table-column>\n        </el-table>\n      </el-form-item>\n    </el-col>\n  </el-row>\n</div>\n", null]}